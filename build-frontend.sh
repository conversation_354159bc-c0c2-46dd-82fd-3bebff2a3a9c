#!/bin/bash

# 前端打包脚本
# 用于构建 InspirFlow 前端应用

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Node.js 和 npm
check_prerequisites() {
    log_info "检查前置条件..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js (>=16.0.0)"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm (>=8.0.0)"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    log_info "Node.js 版本: v$NODE_VERSION"
    
    NPM_VERSION=$(npm --version)
    log_info "npm 版本: $NPM_VERSION"
}

# 清理旧的构建文件
clean_build() {
    log_info "清理旧的构建文件..."
    
    if [ -d "frontend/dist" ]; then
        rm -rf frontend/dist
        log_success "已清理 frontend/dist 目录"
    fi
    
    if [ -d "frontend/node_modules" ]; then
        log_warning "检测到 node_modules 目录，建议重新安装依赖以确保构建环境干净"
        read -p "是否重新安装依赖？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf frontend/node_modules
            log_success "已清理 frontend/node_modules 目录"
        fi
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装前端依赖..."
    
    cd frontend
    
    if [ ! -f "package-lock.json" ]; then
        log_warning "未找到 package-lock.json，将使用 npm install"
        npm install
    else
        log_info "使用 npm ci 安装依赖（更快且更可靠）"
        npm ci
    fi
    
    cd ..
    log_success "依赖安装完成"
}

# 加载环境变量
load_env_vars() {
    log_info "加载环境变量..."
    
    if [ -f ".env" ]; then
        # 导出环境变量，但只导出 VITE_ 开头的变量
        set -a
        source .env
        set +a
        log_success "已加载 .env 文件中的环境变量"
    else
        log_warning "未找到 .env 文件，将使用默认配置"
        if [ -f ".env.example" ]; then
            log_info "发现 .env.example 文件，建议复制并配置为 .env"
        fi
    fi
}

# 构建前端
build_frontend() {
    log_info "开始构建前端应用..."
    
    cd frontend
    
    # 显示构建环境信息
    log_info "构建环境信息:"
    echo "  - VITE_API_BASE_URL: ${VITE_API_BASE_URL:-未设置}"
    echo "  - VITE_DEV_MODE: ${VITE_DEV_MODE:-未设置}"
    echo "  - NODE_ENV: ${NODE_ENV:-production}"
    
    # 设置生产环境
    export NODE_ENV=production
    
    # 执行构建
    npm run build
    
    cd ..
    
    if [ -d "frontend/dist" ]; then
        log_success "前端构建完成！"
        
        # 显示构建结果信息
        DIST_SIZE=$(du -sh frontend/dist | cut -f1)
        FILE_COUNT=$(find frontend/dist -type f | wc -l)
        log_info "构建结果:"
        echo "  - 输出目录: frontend/dist"
        echo "  - 总大小: $DIST_SIZE"
        echo "  - 文件数量: $FILE_COUNT"
        
        # 列出主要文件
        log_info "主要构建文件:"
        ls -la frontend/dist/
    else
        log_error "构建失败：未找到 dist 目录"
        exit 1
    fi
}

# 验证构建结果
validate_build() {
    log_info "验证构建结果..."
    
    # 检查关键文件
    REQUIRED_FILES=("frontend/dist/index.html")
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "缺少关键文件: $file"
            exit 1
        fi
    done
    
    # 检查是否有 JS 和 CSS 文件
    JS_FILES=$(find frontend/dist -name "*.js" | wc -l)
    CSS_FILES=$(find frontend/dist -name "*.css" | wc -l)
    
    if [ $JS_FILES -eq 0 ]; then
        log_error "未找到 JavaScript 文件"
        exit 1
    fi
    
    if [ $CSS_FILES -eq 0 ]; then
        log_warning "未找到 CSS 文件（可能正常）"
    fi
    
    log_success "构建验证通过"
    log_info "找到 $JS_FILES 个 JS 文件和 $CSS_FILES 个 CSS 文件"
}

# 主函数
main() {
    log_info "开始前端打包流程..."
    echo "=================================="
    
    # 检查是否在项目根目录
    if [ ! -f "docker-compose.yml" ] || [ ! -d "frontend" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    check_prerequisites
    clean_build
    load_env_vars
    install_dependencies
    build_frontend
    validate_build
    
    echo "=================================="
    log_success "前端打包完成！"
    log_info "现在可以运行 ./deploy.sh 进行部署"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
