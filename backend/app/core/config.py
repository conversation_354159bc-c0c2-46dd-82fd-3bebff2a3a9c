# app/core/config.py - 应用配置

import os
from typing import List
from pydantic_settings import BaseSettings
from pydantic import Field

class Settings(BaseSettings):
    """应用配置类"""

    # 应用基本配置
    APP_NAME: str = "InspirFlow FastAPI"
    VERSION: str = "2.0.0"
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=20010, env="PORT")
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:20020",
        "http://localhost:5173",
        "http://127.0.0.1:20020",
        "http://**************:20020"
    ]
    
    # JWT配置
    JWT_SECRET_KEY: str = Field(default="your-secret-key-change-in-production", env="JWT_SECRET_KEY")
    JWT_ALGORITHM: str = Field(default="HS256", env="JWT_ALGORITHM")
    JWT_EXPIRE_HOURS: int = Field(default=24, env="JWT_EXPIRE_HOURS")

    # 外部API配置
    MODEL_API_BASE_URL: str = Field(default="http://localhost:8000", env="MODEL_API_BASE_URL")
    MODEL_API_KEY: str = Field(default="demo-key", env="MODEL_API_KEY")
    RECORD_API_BASE_URL: str = Field(default="http://localhost:8001", env="RECORD_API_BASE_URL")
    RECORD_API_KEY: str = Field(default="demo-key", env="RECORD_API_KEY")

    # MinIO配置
    MINIO_ENDPOINT: str = Field(default="localhost:9000", env="MINIO_ENDPOINT")
    MINIO_ACCESS_KEY: str = Field(default="minioadmin", env="MINIO_ACCESS_KEY")
    MINIO_SECRET_KEY: str = Field(default="minioadmin", env="MINIO_SECRET_KEY")
    MINIO_BUCKET: str = Field(default="inspirflow-content", env="MINIO_BUCKET")
    MINIO_SECURE: bool = Field(default=False, env="MINIO_SECURE")
    
    # 上传配置
    MAX_FILE_SIZE: int = Field(default=10 * 1024 * 1024, env="MAX_FILE_SIZE")  # 10MB
    ALLOWED_FILE_TYPES: List[str] = ["image/jpeg", "image/png", "image/gif", "image/webp"]
    
    # 聊天配置
    DEFAULT_MODEL: str = Field(default="gpt-4.1", env="DEFAULT_MODEL")
    DEFAULT_TEMPERATURE: float = Field(default=0.8, env="DEFAULT_TEMPERATURE")
    MAX_TOKENS: int = Field(default=4000, env="MAX_TOKENS")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="DEBUG", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    
    model_config = {
        "extra": "ignore",
        "case_sensitive": True
    }

# 创建全局配置实例
settings = Settings()

# 模型ID映射配置
MODEL_ID_MAPPING = {
    'gpt-3.5-turbo': 1,
    'gpt-4': 2,
    'gpt-4.1': 3,
    'gpt-4-turbo': 4,
    'claude-3-sonnet': 5,
    'claude-3-opus': 6,
    'claude-sonnet-4': 7,
    'gemini-pro': 8,
    'gemini-2.5-pro': 9,
    'gemini-2.5-flash': 10,
    'deepseek-v3': 11,
    'deepseek-r1': 12,
    'o3': 13,
    'o4-mini': 14,
    'gpt-4.1-mini': 15,
    'grok-4': 16,
}

# 反向映射
ID_TO_MODEL_MAPPING = {v: k for k, v in MODEL_ID_MAPPING.items()}

def get_model_id(model_name: str) -> int:
    """获取模型ID"""
    normalized_name = model_name.lower().strip()
    return MODEL_ID_MAPPING.get(normalized_name, 1)  # 默认返回1

def get_model_name(model_id: int) -> str:
    """获取模型名称"""
    return ID_TO_MODEL_MAPPING.get(model_id, 'gpt-3.5-turbo')  # 默认返回gpt-3.5-turbo
