# app/services/chat_service.py - 聊天服务

import json
import httpx
import logging
from typing import Optional, Dict, Any, AsyncGenerator
from datetime import datetime

from ..core.config import settings, get_model_id
from ..services.minio_service import minio_service

logger = logging.getLogger(__name__)

class ChatService:
    """聊天服务类"""

    def __init__(self):
        self.model_api_url = settings.MODEL_API_BASE_URL
        self.record_api_url = settings.RECORD_API_BASE_URL

        # 开发环境内存消息后备存储
        self._fallback_messages = {}

    async def create_user_message(
        self,
        api_key: str,
        conversation_id: int,
        content: str,
        model_name: str = None
    ) -> Optional[Dict[str, Any]]:
        """创建用户消息"""
        try:
            # 尝试处理消息内容并上传到MinIO（生成所有格式）
            temp_message_id = int(datetime.now().timestamp() * 1000000)  # 临时ID
            try:
                message_urls = await minio_service.process_message_content(
                    temp_message_id, content, role="user", conversation_id=conversation_id
                )
            except Exception as e:
                logger.warning(f"MinIO处理失败: {e}，使用模拟URL")
                message_urls = {
                    "original_url": f"mock://message/{temp_message_id}/original",
                    "rendered_katex_url": f"mock://message/{temp_message_id}/katex",
                    "rendered_plain_url": f"mock://message/{temp_message_id}/plain",
                    "rendered_html_url": f"mock://message/{temp_message_id}/html"
                }

            # 获取模型ID
            model_id = get_model_id(model_name) if model_name else 1

            # 尝试创建消息记录
            try:
                async with httpx.AsyncClient() as client:
                    headers = {"Authorization": f"Bearer {settings.RECORD_API_KEY}"}
                    data = {
                        "conversation_id": conversation_id,
                        "role": "user",
                        "content_url": message_urls.get("original_url", "placeholder://empty"),
                        "plain_html_url": message_urls.get("rendered_html_url"),
                        "model_id": model_id
                    }

                    response = await client.post(
                        f"{self.record_api_url}/api/v1/messages/",
                        headers=headers,
                        json=data,
                        timeout=10.0
                    )

                    if response.status_code == 200:
                        message_data = response.json()
                        logger.info(f"用户消息创建成功: ID={message_data.get('id')}")
                        # 将成功创建的消息也写入本地后备，便于Record API读取失败时前端仍能看到
                        try:
                            local_copy = {
                                "id": message_data.get("id", temp_message_id),
                                "conversation_id": conversation_id,
                                "role": "user",
                                "content": content,
                                "content_url": message_urls.get("original_url"),
                                "plain_html_url": message_urls.get("rendered_html_url"),
                                "model_id": model_id,
                                "created_at": message_data.get("created_at") or datetime.now().isoformat(),
                                "is_mock": False
                            }
                            self._fallback_messages.setdefault(conversation_id, []).append(local_copy)
                        except Exception:
                            pass
                        return message_data
                    else:
                        logger.warning(f"Record API创建消息失败: HTTP {response.status_code}")
                        raise Exception(f"Record API失败: {response.status_code}")

            except Exception as record_error:
                logger.warning(f"Record API不可用: {record_error}，使用模拟消息记录")

                # 创建模拟的消息记录并放入内存后备
                mock_message = {
                    "id": temp_message_id,
                    "conversation_id": conversation_id,
                    "role": "user",
                    "content": content,
                    "content_url": message_urls.get("original_url"),
                    "plain_html_url": message_urls.get("rendered_html_url"),
                    "model_id": model_id,
                    "created_at": datetime.now().isoformat(),
                    "is_mock": True
                }
                self._fallback_messages.setdefault(conversation_id, []).append(mock_message)
                logger.info(f"模拟用户消息创建成功: ID={temp_message_id}")
                return mock_message

        except Exception as e:
            logger.error(f"创建用户消息异常: {e}")
            return None

    async def generate_ai_response(
        self,
        conversation_id: int,
        model_name: str,
        user_message: str,
        temperature: float = 0.8,
        max_tokens: int = 4000,
        stream: bool = False,
        api_key: str = ""
    ) -> Optional[str]:
        """生成AI回复"""
        print(f"🚀 开始生成AI回复，对话ID: {conversation_id}, 模型: {model_name}")
        try:
            # 首先尝试真实的API调用
            try:
                # 获取对话历史作为上下文
                print(f"🔍 获取对话 {conversation_id} 的历史消息作为上下文...")
                logger.info(f"🔍 获取对话 {conversation_id} 的历史消息作为上下文...")
                history = await self.get_conversation_history(api_key, conversation_id, limit=10)
                print(f"📚 获取到历史消息: {len(history) if history else 0} 条")

                # 构建消息列表，包含历史上下文
                messages = history.copy() if history else []
                print(f"📚 加载了 {len(messages)} 条历史消息作为上下文")
                logger.info(f"📚 加载了 {len(messages)} 条历史消息作为上下文")

                # 将历史消息中的 data:image 内联图片也转换为多模态结构，避免后续轮次丢失图片信息
                try:
                    import re as _re
                    def _to_mm(msg: dict) -> dict:
                        c = msg.get("content")
                        if isinstance(c, str):
                            urls = _re.findall(r'!\[[^\]]*\]\((data:image[^)]+)\)', c)
                            if urls:
                                try:
                                    text_only = _re.sub(r'!\[[^\]]*\]\(data:[^)]+\)', '', c).strip()
                                except Exception:
                                    text_only = c
                                parts = []
                                if text_only:
                                    parts.append({"type": "text", "text": text_only})
                                for u in urls:
                                    parts.append({"type": "image_url", "image_url": {"url": u}})
                                return {"role": msg.get("role", "user"), "content": parts}
                        return msg
                    messages = [_to_mm(m) for m in messages]
                except Exception as _:
                    pass

                # 添加当前用户消息（支持内联图片的多模态格式）
                try:
                    import re
                    data_urls = re.findall(r'!\[[^\]]*\]\((data:image[^)]+)\)', user_message or "")
                except Exception:
                    data_urls = []

                if data_urls:
                    # 去掉markdown中的图片占位，保留文本
                    try:
                        text_only = re.sub(r'!\[[^\]]*\]\(data:[^)]+\)', '', user_message).strip()
                    except Exception:
                        text_only = user_message

                    content_parts = []
                    if text_only:
                        content_parts.append({"type": "text", "text": text_only})
                    for url in data_urls:
                        # OpenAI Chat Completions 支持 {type:"image_url", image_url:{url}}
                        content_parts.append({
                            "type": "image_url",
                            "image_url": {"url": url}
                        })
                    messages.append({"role": "user", "content": content_parts})
                else:
                    messages.append({
                        "role": "user",
                        "content": user_message
                    })

                print(f"💬 最终消息列表包含 {len(messages)} 条消息（包括当前用户消息）")
                logger.info(f"💬 最终消息列表包含 {len(messages)} 条消息（包括当前用户消息）")

                # 构建聊天请求（OpenAI格式）
                payload = {
                    "model": model_name,
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    "stream": stream
                }

                async with httpx.AsyncClient() as client:
                    # 使用配置的Model API密钥
                    headers = {"Authorization": f"Bearer {settings.MODEL_API_KEY}"}
                    timeout = httpx.Timeout(10.0)
                    response = await client.post(
                        f"{self.model_api_url}/api/v1/chat/completions",
                        headers=headers,
                        json=payload,
                        timeout=timeout
                    )

                    if response.status_code == 200:
                        if stream:
                            # 处理流式响应（usage 暂不可用）
                            content = await self._handle_stream_response(response)
                            return {"content": content, "usage": None}
                        else:
                            # 处理普通响应
                            data = response.json()
                            usage = data.get("usage") or data.get("token_usage")
                            if "choices" in data and len(data["choices"]) > 0:
                                content = data["choices"][0]["message"].get("content")
                                logger.info("AI回复生成成功")
                                return {"content": content, "usage": usage}
                            else:
                                logger.error("AI回复格式异常")
                                raise Exception("API回复格式异常")
                    else:
                        logger.error(f"AI回复生成失败: HTTP {response.status_code}")
                        raise Exception(f"API调用失败: {response.status_code}")

            except Exception as api_error:
                logger.error(f"Model API调用失败: {api_error}")
                raise Exception(f"AI回复生成失败: {api_error}")

        except httpx.TimeoutException:
            logger.error("AI回复生成超时")
            return "抱歉，回复生成超时，请稍后重试。"
        except Exception as e:
            logger.error(f"AI回复生成异常: {e}")
            return f"抱歉，我遇到了一个问题：{str(e)[:100]}..."

    async def create_ai_message(
        self,
        api_key: str,
        conversation_id: int,
        content: str,
        model_name: str = None,
        is_error: bool = False
    ) -> Optional[Dict[str, Any]]:
        """创建AI消息"""
        try:
            # 尝试处理消息内容并上传到MinIO
            temp_message_id = int(datetime.now().timestamp() * 1000000)
            try:
                message_urls = await minio_service.process_message_content(
                    temp_message_id, content, role="assistant", conversation_id=conversation_id
                )
            except Exception as e:
                logger.warning(f"MinIO处理失败: {e}，使用模拟URL")
                message_urls = {
                    "original_url": f"mock://message/{temp_message_id}/original",
                    "rendered_katex_url": f"mock://message/{temp_message_id}/katex",
                    "rendered_plain_url": f"mock://message/{temp_message_id}/plain",
                    "rendered_html_url": f"mock://message/{temp_message_id}/html"
                }

            # 获取模型ID
            model_id = get_model_id(model_name) if model_name else 1

            # 尝试创建消息记录
            try:
                async with httpx.AsyncClient() as client:
                    headers = {"Authorization": f"Bearer {settings.RECORD_API_KEY}"}
                    data = {
                        "conversation_id": conversation_id,
                        "role": "assistant",
                        "content_url": message_urls.get("original_url", "placeholder://empty"),
                        "plain_html_url": message_urls.get("rendered_html_url"),
                        "model_id": model_id,
                        "is_error": is_error
                    }

                    response = await client.post(
                        f"{self.record_api_url}/api/v1/messages/",
                        headers=headers,
                        json=data,
                        timeout=10.0
                    )

                    if response.status_code == 200:
                        message_data = response.json()
                        real_message_id = message_data.get('id')
                        logger.info(f"AI消息创建成功: ID={real_message_id}")
                        # 同样写入本地后备，防止后续Record API拉取失败时前端无历史
                        try:
                            local_copy = {
                                "id": message_data.get("id", temp_message_id),
                                "conversation_id": conversation_id,
                                "role": "assistant",
                                "content": content,
                                "content_url": message_urls.get("original_url"),
                                "plain_html_url": message_urls.get("rendered_html_url"),
                                "model_id": model_id,
                                "is_error": is_error,
                                "created_at": message_data.get("created_at") or datetime.now().isoformat(),
                                "is_mock": False
                            }
                            self._fallback_messages.setdefault(conversation_id, []).append(local_copy)
                        except Exception:
                            pass
                        return message_data
                    else:
                        logger.warning(f"Record API创建AI消息失败: HTTP {response.status_code}")
                        raise Exception(f"Record API失败: {response.status_code}")

            except Exception as record_error:
                logger.warning(f"Record API不可用: {record_error}，使用模拟AI消息记录")

                # 创建模拟的AI消息记录并放入内存后备
                mock_message = {
                    "id": temp_message_id,
                    "conversation_id": conversation_id,
                    "role": "assistant",
                    "content": content,
                    "content_url": message_urls.get("original_url"),
                    "plain_html_url": message_urls.get("rendered_html_url"),
                    "model_id": model_id,
                    "is_error": is_error,
                    "created_at": datetime.now().isoformat(),
                    "is_mock": True
                }
                self._fallback_messages.setdefault(conversation_id, []).append(mock_message)
                logger.info(f"模拟AI消息创建成功: ID={temp_message_id}")
                return mock_message

        except Exception as e:
            logger.error(f"创建AI消息异常: {e}")
            return None

    async def _handle_stream_response(self, response: httpx.Response) -> str:
        """处理流式响应"""
        content_parts = []

        async for line in response.aiter_lines():
            if line.startswith("data: "):
                data_str = line[6:]  # 移除 "data: " 前缀

                if data_str == "[DONE]":
                    break

                try:
                    data = json.loads(data_str)
                    if "choices" in data and len(data["choices"]) > 0:
                        delta = data["choices"][0].get("delta", {})
                        if "content" in delta:
                            content_parts.append(delta["content"])
                except json.JSONDecodeError:
                    continue

        return "".join(content_parts)

    async def get_conversation_history(
        self,
        api_key: str,
        conversation_id: int,
        limit: int = 10
    ) -> list:
        """获取对话历史（用于AI聊天的简化格式）"""
        """获取对话历史"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {"Authorization": f"Bearer {settings.RECORD_API_KEY}"}
                response = await client.get(
                    f"{self.record_api_url}/api/v1/messages/",
                    headers=headers,
                    params={"conversation_id": conversation_id},
                    timeout=10.0
                )

                if response.status_code == 200:
                    messages = response.json()

                    # 转换为聊天格式
                    chat_messages = []
                    for msg in messages[-limit:]:  # 只取最近的消息
                        role = msg.get("role")
                        if role in ["user", "assistant"]:
                            content = await self._get_message_content(msg)
                            if content:
                                chat_messages.append({
                                    "role": role,
                                    "content": content
                                })
                    return chat_messages
                else:
                    logger.error(f"获取对话历史失败: HTTP {response.status_code}")
                    # 后备：使用内存消息
                    fallback = self._fallback_messages.get(conversation_id, [])[-limit:]
                    return [{"role": m.get("role"), "content": await self._get_message_content(m) or ""} for m in fallback]

        except Exception as e:
            logger.error(f"获取对话历史异常: {e}")
            return []

    async def get_conversation_messages(
        self,
        conversation_id: int,
        user_mathjax_setting: bool = False,
        limit: int = 50,
        auth_api_key: str | None = None,
    ) -> list:
        """获取对话消息（用于前端显示的完整格式）"""
        try:
            async with httpx.AsyncClient() as client:
                async def fetch(headers: dict, with_pagination: bool = True):
                    params = {"conversation_id": conversation_id}
                    if with_pagination:
                        params.update({"page": 1, "size": max(1, min(limit, 100))})
                    return await client.get(
                        f"{self.record_api_url}/api/v1/messages/",
                        headers=headers,
                        params=params,
                        timeout=10.0
                    )

                response = None
                # 优先用用户密钥（更符合权限模型）
                if auth_api_key:
                    try:
                        response = await fetch({"Authorization": f"Bearer {auth_api_key}"}, with_pagination=True)
                        if response.status_code != 200:
                            # 再试不带分页参数
                            response = await fetch({"Authorization": f"Bearer {auth_api_key}"}, with_pagination=False)
                    except Exception:
                        response = None

                # 失败则使用管理员密钥兜底
                if not response or response.status_code != 200:
                    try:
                        response = await fetch({"Authorization": f"Bearer {settings.RECORD_API_KEY}"}, with_pagination=True)
                        if response.status_code != 200:
                            response = await fetch({"Authorization": f"Bearer {settings.RECORD_API_KEY}"}, with_pagination=False)
                    except Exception:
                        pass

                if response.status_code == 200:
                    messages = response.json()

                    # 处理消息，根据用户设置选择正确的URL
                    processed_messages = []
                    for msg in messages[-limit:]:  # 只取最近的消息
                        processed_msg = {
                            "id": msg.get("id"),
                            "conversation_id": msg.get("conversation_id"),
                            "role": msg.get("role"),
                            "created_at": msg.get("created_at"),
                            "updated_at": msg.get("updated_at"),
                            "model_id": msg.get("model_id"),
                            "temperature": msg.get("temperature"),
                            "max_tokens": msg.get("max_tokens"),
                            "is_error": msg.get("is_error", False),
                            "error_info": msg.get("error_info"),
                            "cost": msg.get("cost")
                        }

                        # 选择展示URL：优先plain_html_url，其次content_url；兼容旧字段 rendered_plain_url/original_url
                        display_url = (
                            msg.get("plain_html_url") or
                            msg.get("content_url") or
                            msg.get("rendered_plain_url") or
                            msg.get("original_url")
                        )
                        processed_msg["content_url"] = display_url
                        processed_msg["content_type"] = "html" if display_url == msg.get("plain_html_url") or display_url == msg.get("rendered_plain_url") else "original"

                        # 保留字段以备前端需要
                        processed_msg["content_url_raw"] = msg.get("content_url")
                        processed_msg["plain_html_url"] = msg.get("plain_html_url")
                        processed_msg["original_url"] = msg.get("original_url")
                        processed_msg["rendered_plain_url"] = msg.get("rendered_plain_url")

                        processed_messages.append(processed_msg)

                    return processed_messages
                else:
                    logger.error(f"获取对话消息失败: HTTP {response.status_code}")
                    # 尝试从 /api/v1/conversations/{id}/messages 端点再拉一次（某些Record API部署只实现了该端点）
                    try:
                        async with httpx.AsyncClient() as client2:
                            headers2 = {"Authorization": f"Bearer {auth_api_key or settings.RECORD_API_KEY}"}
                            resp2 = await client2.get(
                                f"{self.record_api_url}/api/v1/conversations/{conversation_id}/messages",
                                headers=headers2,
                                timeout=10.0
                            )
                            if resp2.status_code == 200:
                                messages = resp2.json().get("messages") or resp2.json()
                            else:
                                messages = None
                    except Exception:
                        messages = None

                    if messages is None:
                        # 后备：返回内存消息
                        fallback_msgs = self._fallback_messages.get(conversation_id, [])[-limit:]
                    processed = []
                    for msg in fallback_msgs:
                        m = {
                            "id": msg.get("id"),
                            "conversation_id": msg.get("conversation_id"),
                            "role": msg.get("role"),
                            "created_at": msg.get("created_at"),
                            "updated_at": msg.get("created_at"),
                            "model_id": msg.get("model_id"),
                            "temperature": None,
                            "max_tokens": None,
                            "is_error": msg.get("is_error", False),
                            "error_info": msg.get("error_info"),
                            "cost": None,
                            "original_url": msg.get("original_url"),
                            "rendered_katex_url": msg.get("rendered_katex_url"),
                            "rendered_plain_url": msg.get("rendered_plain_url"),
                            "content": msg.get("content"),
                        }
                        # 选择展示URL：优先plain_html_url，其次content_url；兼容旧字段
                        display_url = (
                            msg.get("plain_html_url") or
                            msg.get("content_url") or
                            msg.get("rendered_plain_url") or
                            msg.get("original_url")
                        )
                        m["content_url"] = display_url
                        m["content_type"] = "html" if display_url == msg.get("plain_html_url") or display_url == msg.get("rendered_plain_url") else "original"
                        processed.append(m)
                    return processed

        except Exception as e:
            logger.error(f"获取对话消息异常: {e}")
            return []

    async def _get_message_content(self, message: Dict[str, Any]) -> Optional[str]:
        """从消息记录中获取内容"""
        # 兼容新字段：优先从 content_url 获取原始内容（通常是JSON）；否则尝试 plain_html_url
        original_url = message.get("content_url") or message.get("original_url")
        if not original_url or str(original_url).startswith("placeholder://"):
            return None

        try:
            content = await minio_service.download_content_from_url(original_url)
            return content
        except Exception as e:
            logger.error(f"获取消息内容异常: {e}")
            return None

# 创建全局聊天服务实例
chat_service = ChatService()
