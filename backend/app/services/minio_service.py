# app/services/minio_service.py - MinIO服务

import json
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from minio import Minio
from minio.error import S3Error
import io
import markdown

from ..core.config import settings

logger = logging.getLogger(__name__)

class MinIOService:
    """MinIO对象存储服务"""

    def __init__(self):
        self.endpoint = settings.MINIO_ENDPOINT
        self.access_key = settings.MINIO_ACCESS_KEY
        self.secret_key = settings.MINIO_SECRET_KEY
        self.bucket_name = settings.MINIO_BUCKET
        self.secure = settings.MINIO_SECURE

        try:
            # 初始化MinIO客户端（仅配置，不进行任何网络请求）
            self.client = Minio(
                self.endpoint,
                access_key=self.access_key,
                secret_key=self.secret_key,
                secure=self.secure
            )
            # 为避免应用启动时阻塞，bucket检查与创建延后到实际使用阶段
            logger.info(f"MinIO客户端已配置（延迟检查bucket）: {self.endpoint}/{self.bucket_name}")
        except Exception as e:
            logger.error(f"MinIO服务初始化失败: {e}")
            # 初始化客户端本身通常不会抛错，这里仅保底记录
            pass

    def _ensure_bucket_exists(self):
        """确保bucket存在并设置公共读取权限（按需调用）"""
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                logger.info(f"创建bucket: {self.bucket_name}")
            # 设置公共读取权限
            self._set_bucket_public_read_policy()
        except S3Error as e:
            logger.error(f"确保bucket存在失败: {e}")
            raise

    def _set_bucket_public_read_policy(self):
        """设置bucket的公共读取权限"""
        try:
            policy = {
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Effect": "Allow",
                        "Principal": {"AWS": "*"},
                        "Action": ["s3:GetObject"],
                        "Resource": [f"arn:aws:s3:::{self.bucket_name}/*"]
                    }
                ]
            }

            self.client.set_bucket_policy(self.bucket_name, json.dumps(policy))
            logger.info(f"已设置bucket {self.bucket_name} 的公共读取权限")

        except S3Error as e:
            logger.warning(f"设置bucket公共读取权限失败: {e}")

    def _generate_object_name(self, message_id: int, content_type: str, file_extension: str = "") -> str:
        """生成对象名称"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"messages/{message_id}/{content_type}_{timestamp}{file_extension}"

    async def upload_original_content(self, message_id: int, content: str, role: str = "user", conversation_id: int = None) -> Optional[str]:
        """上传原始消息内容到MinIO"""
        try:
            # 验证输入参数
            if not content or not content.strip():
                logger.warning(f"消息内容为空 - 消息ID: {message_id}")
                content = ""  # 确保content不是None

            # 创建JSON格式的内容，包含完整的消息头信息
            content_data = {
                "message_id": message_id,
                "conversation_id": conversation_id,
                "role": role,
                "content": content,
                "type": "text",
                "timestamp": datetime.now().isoformat(),
                "format": "original"
            }

            # 序列化JSON
            content_json = json.dumps(content_data, ensure_ascii=False, indent=2)
            content_bytes = content_json.encode('utf-8')

            # 详细日志
            logger.info(f"准备上传原始内容 - 消息ID: {message_id}")
            logger.info(f"  - 内容长度: {len(content)} 字符")
            logger.info(f"  - JSON长度: {len(content_json)} 字节")
            logger.debug(f"  - 原始内容: {repr(content)}")

            # 生成对象名称
            object_name = self._generate_object_name(message_id, "original", ".json")
            logger.info(f"  - 对象名称: {object_name}")

            # 确保bucket存在（按需）
            try:
                self._ensure_bucket_exists()
            except Exception as e:
                logger.warning(f"跳过bucket检查: {e}")
            # 上传到MinIO
            result = self.client.put_object(
                self.bucket_name,
                object_name,
                io.BytesIO(content_bytes),
                len(content_bytes),
                content_type="application/json"
            )
            logger.info(f"  - MinIO上传结果: {result}")

            # 生成预签名URL
            url = self.client.presigned_get_object(
                self.bucket_name,
                object_name,
                expires=timedelta(days=7)  # 7天有效期
            )

            logger.info(f"✅ 原始内容上传成功: {url}")

            # 验证上传结果
            try:
                # 立即尝试下载验证
                response = self.client.get_object(self.bucket_name, object_name)
                downloaded_content = response.read().decode('utf-8')
                downloaded_data = json.loads(downloaded_content)

                if downloaded_data.get("content") != content:
                    logger.error(f"❌ 上传验证失败 - 内容不匹配")
                    logger.error(f"  - 原始: {repr(content)}")
                    logger.error(f"  - 下载: {repr(downloaded_data.get('content'))}")
                else:
                    logger.info(f"✅ 上传验证成功 - 内容匹配")

            except Exception as verify_error:
                logger.warning(f"⚠️ 上传验证失败: {verify_error}")

            return url

        except Exception as e:
            logger.error(f"❌ 上传原始内容失败: {e}")
            logger.error(f"  - 消息ID: {message_id}")
            logger.error(f"  - 内容: {repr(content)}")
            import traceback
            logger.error(f"  - 堆栈: {traceback.format_exc()}")
            return None

    async def upload_rendered_html(
        self,
        message_id: int,
        content: str,
        with_katex: bool = True
    ) -> Optional[str]:
        """上传渲染后的HTML内容到MinIO"""
        try:
            # 验证输入参数
            if not content or not content.strip():
                logger.warning(f"HTML渲染内容为空 - 消息ID: {message_id}")
                content = ""  # 确保content不是None

            content_type = "rendered_katex" if with_katex else "rendered_plain"
            logger.info(f"准备上传{content_type}内容 - 消息ID: {message_id}")
            logger.info(f"  - 原始内容长度: {len(content)} 字符")
            logger.debug(f"  - 原始内容: {repr(content)}")

            # 创建完整的HTML文档（会在内部进行markdown渲染）
            full_html = self._create_full_html_document(content, with_katex)
            html_bytes = full_html.encode('utf-8')

            logger.info(f"  - HTML文档长度: {len(full_html)} 字符")

            # 生成对象名称
            object_name = self._generate_object_name(message_id, content_type, ".html")
            logger.info(f"  - 对象名称: {object_name}")

            # 确保bucket存在（按需）
            try:
                self._ensure_bucket_exists()
            except Exception as e:
                logger.warning(f"跳过bucket检查: {e}")
            # 上传到MinIO
            result = self.client.put_object(
                self.bucket_name,
                object_name,
                io.BytesIO(html_bytes),
                len(html_bytes),
                content_type="text/html"
            )
            logger.info(f"  - MinIO上传结果: {result}")

            # 生成预签名URL
            url = self.client.presigned_get_object(
                self.bucket_name,
                object_name,
                expires=timedelta(days=7)  # 7天有效期
            )

            logger.info(f"✅ {content_type}上传成功: {url}")
            return url

        except Exception as e:
            logger.error(f"❌ 上传{content_type}失败: {e}")
            logger.error(f"  - 消息ID: {message_id}")
            logger.error(f"  - 内容: {repr(content)}")
            import traceback
            logger.error(f"  - 堆栈: {traceback.format_exc()}")
            return None

    def _render_markdown_to_html(self, content: str) -> str:
        """将markdown内容渲染为HTML，保护LaTeX语法和代码块"""
        try:
            # 保护LaTeX公式和代码块，避免markdown处理破坏它们
            protected_content = content
            placeholders = {}
            placeholder_index = 0

            # 保护块级公式 $$...$$
            def protect_block_math(match):
                nonlocal placeholder_index
                placeholder = f"MATHBLOCKPLACEHOLDER{placeholder_index}MATHBLOCKPLACEHOLDER"
                placeholders[placeholder] = match.group(0)
                placeholder_index += 1
                return placeholder

            # 保护行内公式 $...$
            def protect_inline_math(match):
                nonlocal placeholder_index
                placeholder = f"MATHINLINEPLACEHOLDER{placeholder_index}MATHINLINEPLACEHOLDER"
                placeholders[placeholder] = match.group(0)
                placeholder_index += 1
                return placeholder

            # 保护代码块 ```...```
            def protect_code_block(match):
                nonlocal placeholder_index
                placeholder = f"CODEBLOCKPLACEHOLDER{placeholder_index}CODEBLOCKPLACEHOLDER"
                # 将markdown代码块转换为HTML格式，保持语言信息
                full_match = match.group(0)
                language = match.group(1) if match.group(1) else ''
                code_content = match.group(2)

                # 转换为HTML格式，供前端处理
                if language:
                    html_code_block = f'<pre><code class="language-{language}">{code_content}</code></pre>'
                else:
                    html_code_block = f'<pre><code>{code_content}</code></pre>'

                placeholders[placeholder] = html_code_block
                placeholder_index += 1
                return placeholder

            # 保护行内代码 `...`
            def protect_inline_code(match):
                nonlocal placeholder_index
                placeholder = f"INLINECODEPLACEHOLDER{placeholder_index}INLINECODEPLACEHOLDER"
                code_content = match.group(1)
                html_inline_code = f'<code>{code_content}</code>'
                placeholders[placeholder] = html_inline_code
                placeholder_index += 1
                return placeholder

            import re
            # 先保护块级公式（支持多行）
            protected_content = re.sub(r'\$\$([\s\S]*?)\$\$', protect_block_math, protected_content)
            # 再保护行内公式
            protected_content = re.sub(r'\$([^$\n]+)\$', protect_inline_math, protected_content)

            # 保护代码块（支持语言标识）
            protected_content = re.sub(r'```(\w+)?\n([\s\S]*?)\n```', protect_code_block, protected_content)
            # 保护行内代码
            protected_content = re.sub(r'`([^`\n]+)`', protect_inline_code, protected_content)

            # 配置markdown扩展（移除codehilite，避免冲突）
            md = markdown.Markdown(extensions=[
                'extra',  # 支持表格等
                'toc',  # 目录
            ])

            # 渲染markdown为HTML
            html_content = md.convert(protected_content)

            # 恢复所有保护的内容
            for placeholder, original_content in placeholders.items():
                html_content = html_content.replace(placeholder, original_content)

            return html_content
        except Exception as e:
            logger.warning(f"Markdown渲染失败: {e}，使用原始内容")
            # 如果渲染失败，至少进行基本的HTML转义
            import html
            return html.escape(content).replace('\n', '<br>')

    def _create_full_html_document(self, content: str, with_katex: bool = True) -> str:
        """创建完整的HTML文档"""
        # 先将markdown渲染为HTML
        rendered_content = self._render_markdown_to_html(content)
        katex_css = """
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
        <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
        <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js"></script>
        """ if with_katex else ""

        katex_script = """
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: "$$", right: "$$", display: true},
                        {left: "$", right: "$", display: false},
                        {left: "\\\\[", right: "\\\\]", display: true},
                        {left: "\\\\(", right: "\\\\)", display: false}
                    ]
                });
            });
        </script>
        """ if with_katex else ""

        html_template = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>InspirFlow Message</title>
            {katex_css}
            <style>
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #fff;
                }}
                .message-content {{
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 20px;
                    border-left: 4px solid #007bff;
                }}
                pre {{
                    background: #f1f3f4;
                    padding: 12px;
                    border-radius: 4px;
                    overflow-x: auto;
                }}
                code {{
                    background: #f1f3f4;
                    padding: 2px 4px;
                    border-radius: 3px;
                    font-family: 'Monaco', 'Consolas', monospace;
                }}
                blockquote {{
                    border-left: 4px solid #ddd;
                    margin: 0;
                    padding-left: 16px;
                    color: #666;
                }}
            </style>
        </head>
        <body>
            <div class="message-content">
                {rendered_content}
            </div>
            {katex_script}
        </body>
        </html>
        """

        return html_template

    def _create_html_document_for_frontend_math(self, content: str) -> str:
        """创建HTML文档，markdown渲染但保留LaTeX语法供前端处理"""
        # 渲染markdown但保留LaTeX语法
        rendered_content = self._render_markdown_to_html(content)

        return f"""<!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>InspirFlow Message</title>
            <style>
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #fff;
                }}
                .message-content {{
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 20px;
                    border-left: 4px solid #007bff;
                }}
                pre {{
                    background: #f1f3f4;
                    padding: 12px;
                    border-radius: 4px;
                    overflow-x: auto;
                }}
                code {{
                    background: #f1f3f4;
                    padding: 2px 4px;
                    border-radius: 3px;
                    font-family: 'Monaco', 'Consolas', monospace;
                }}
                blockquote {{
                    border-left: 4px solid #ddd;
                    margin: 0;
                    padding-left: 16px;
                    color: #666;
                }}
                table {{
                    border-collapse: collapse;
                    width: 100%;
                }}
                th, td {{
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }}
                th {{
                    background-color: #f2f2f2;
                }}
            </style>
        </head>
        <body>
            <div class="message-content">
                {rendered_content}
            </div>
        </body>
        </html>"""

    async def upload_rendered_html_for_frontend(self, message_id: int, content: str) -> Optional[str]:
        """上传为前端数学渲染优化的HTML内容"""
        try:
            # 验证输入参数
            if not content or not content.strip():
                logger.warning(f"HTML渲染内容为空 - 消息ID: {message_id}")
                content = ""

            logger.info(f"准备上传前端数学渲染HTML - 消息ID: {message_id}")
            logger.info(f"  - 原始内容长度: {len(content)} 字符")
            logger.debug(f"  - 原始内容: {repr(content)}")

            # 创建为前端数学渲染优化的HTML文档
            full_html = self._create_html_document_for_frontend_math(content)
            html_bytes = full_html.encode('utf-8')

            logger.info(f"  - HTML文档长度: {len(full_html)} 字符")

            # 生成对象名称
            object_name = self._generate_object_name(message_id, "rendered_html", ".html")
            logger.info(f"  - 对象名称: {object_name}")

            # 确保bucket存在（按需）
            try:
                self._ensure_bucket_exists()
            except Exception as e:
                logger.warning(f"跳过bucket检查: {e}")
            # 确保bucket存在（按需）
            try:
                self._ensure_bucket_exists()
            except Exception as e:
                logger.warning(f"跳过bucket检查: {e}")
            # 上传到MinIO
            result = self.client.put_object(
                self.bucket_name,
                object_name,
                io.BytesIO(html_bytes),
                len(html_bytes),
                content_type="text/html"
            )
            logger.info(f"  - MinIO上传结果: {result}")

            # 生成预签名URL
            url = self.client.presigned_get_object(
                self.bucket_name,
                object_name,
                expires=timedelta(days=7)
            )

            logger.info(f"✅ 前端数学渲染HTML上传成功: {url}")
            return url

        except Exception as e:
            logger.error(f"❌ 上传前端数学渲染HTML失败: {e}")
            logger.error(f"  - 消息ID: {message_id}")
            logger.error(f"  - 内容: {repr(content)}")
            import traceback
            logger.error(f"  - 堆栈: {traceback.format_exc()}")
            return None

    async def process_message_content(self, message_id: int, content: str, role: str = "assistant", conversation_id: int = None) -> Dict[str, Optional[str]]:
        """处理消息内容，生成两种格式的URL（优化版）

        Args:
            message_id: 消息ID
            content: 原始消息内容（markdown格式）
            role: 消息角色（user/assistant）
            conversation_id: 对话ID

        Returns:
            包含两种格式URL的字典：
            - original_url: 原始JSON格式
            - rendered_html_url: markdown渲染的HTML（保留LaTeX语法供前端处理）
        """
        logger.info(f"🔄 开始处理消息内容（前端数学渲染模式） - 消息ID: {message_id}, 角色: {role}")
        logger.info(f"  - 对话ID: {conversation_id}")
        logger.info(f"  - 内容长度: {len(content)} 字符")

        result = {
            "original_url": None,
            "rendered_html_url": None,
            # 保留旧字段以兼容现有代码
            "rendered_katex_url": None,
            "rendered_plain_url": None
        }

        try:
            # 1. 上传原始内容（JSON格式）
            logger.info("📄 步骤1: 上传原始内容...")
            original_url = await self.upload_original_content(message_id, content, role, conversation_id)
            result["original_url"] = original_url

            if not original_url:
                logger.error("❌ 原始内容上传失败，跳过后续步骤")
                return result

            # 2. 渲染并上传HTML内容（为前端数学渲染优化）
            logger.info("🎨 步骤2: 渲染并上传前端数学渲染HTML...")
            rendered_html_url = await self.upload_rendered_html_for_frontend(message_id, content)
            result["rendered_html_url"] = rendered_html_url

            # 为了兼容现有前端代码，同时设置旧字段
            result["rendered_katex_url"] = rendered_html_url
            result["rendered_plain_url"] = rendered_html_url

            # 统计结果
            success_count = sum(1 for url in [original_url, rendered_html_url] if url is not None)
            logger.info(f"✅ 消息内容处理完成 - 成功: {success_count}/2")

            return result

        except Exception as e:
            logger.error(f"❌ 处理消息内容失败: {e}")
            logger.error(f"  - 消息ID: {message_id}")
            logger.error(f"  - 内容: {repr(content)}")
            import traceback
            logger.error(f"  - 堆栈: {traceback.format_exc()}")
            return result

    async def download_content_from_url(self, url: str) -> Optional[str]:
        """从 MinIO URL 下载文本内容（HTML/JSON/TEXT），失败返回 None"""
        try:
            if not url or url.startswith("mock://") or url.startswith("placeholder://"):
                return None

            import urllib.parse, json
            parsed_url = urllib.parse.urlparse(url)

            # 如果是 http/https 预签名URL，直接通过HTTP获取文本内容
            if parsed_url.scheme in ("http", "https"):
                try:
                    import httpx
                    async with httpx.AsyncClient() as client:
                        resp = await client.get(url, timeout=15.0)
                        if resp.status_code == 200:
                            text = resp.text
                            # 如果是 JSON，尝试提取 content 字段
                            if 'application/json' in (resp.headers.get('content-type') or '').lower():
                                try:
                                    data = resp.json()
                                    return data.get('content', text)
                                except Exception:
                                    return text
                            return text
                        else:
                            logger.warning(f"HTTP获取失败: {resp.status_code} {url}")
                except Exception as e:
                    logger.warning(f"HTTP获取异常: {e}")

            # 提取 bucket 与 object，尽量从URL中推断bucket，推断失败回退到默认bucket
            bucket = None
            object_name = None

            if parsed_url.scheme in ("minio", "s3"):
                # 例如: minio://bucket/path/to/object
                bucket = parsed_url.netloc or None
                object_name = parsed_url.path.lstrip('/')
            else:
                # http(s) 预签名：通常为 /<bucket>/<object>
                path_lstr = parsed_url.path.lstrip('/')
                if '/' in path_lstr:
                    bucket_candidate, rest = path_lstr.split('/', 1)
                    bucket = bucket_candidate or None
                    object_name = rest
                else:
                    object_name = path_lstr

                # 兼容此前把默认bucket拼到路径里的情况
                if object_name and object_name.startswith(f"{self.bucket_name}/"):
                    object_name = object_name[len(self.bucket_name)+1:]

            if not object_name:
                logger.warning(f"无法从URL中提取对象名称: {url}")
                return None

            use_bucket = bucket or self.bucket_name

            # 从 MinIO 获取对象
            response = self.client.get_object(use_bucket, object_name)
            raw = response.read()
            response.close()
            response.release_conn()
            content = raw.decode('utf-8', errors='ignore')

            # 如果是 JSON，尝试提取 content 字段
            if object_name.endswith('.json'):
                try:
                    data = json.loads(content)
                    return data.get('content', content)
                except Exception:
                    return content

            return content
        except Exception as e:
            logger.error(f"下载文本内容失败: {e}")
            return None

    async def download_bytes_from_url(self, url: str):
        """从 MinIO URL 下载原始字节内容，返回 (bytes, content_type) 或 (None, None)"""
        try:
            if not url or url.startswith("mock://") or url.startswith("placeholder://"):
                return None, None
            import urllib.parse
            parsed_url = urllib.parse.urlparse(url)

            # 如果是 http/https 预签名URL，直接通过HTTP获取字节内容
            if parsed_url.scheme in ("http", "https"):
                try:
                    import httpx
                    async with httpx.AsyncClient() as client:
                        resp = await client.get(url, timeout=15.0)
                        if resp.status_code == 200:
                            data = resp.content
                            content_type = resp.headers.get('content-type') or 'application/octet-stream'
                            return data, content_type
                        else:
                            logger.warning(f"HTTP获取失败: {resp.status_code} {url}")
                except Exception as e:
                    logger.warning(f"HTTP获取异常: {e}")

            # 提取 bucket 与 object
            bucket = None
            object_name = None
            if parsed_url.scheme in ("minio", "s3"):
                bucket = parsed_url.netloc or None
                object_name = parsed_url.path.lstrip('/')
            else:
                path_lstr = parsed_url.path.lstrip('/')
                if '/' in path_lstr:
                    bucket_candidate, rest = path_lstr.split('/', 1)
                    bucket = bucket_candidate or None
                    object_name = rest
                else:
                    object_name = path_lstr

                if object_name and object_name.startswith(f"{self.bucket_name}/"):
                    object_name = object_name[len(self.bucket_name)+1:]

            if not object_name:
                logger.warning(f"无法从URL中提取对象名称: {url}")
                return None, None

            use_bucket = bucket or self.bucket_name

            response = self.client.get_object(use_bucket, object_name)
            data = response.read()
            response.close()
            response.release_conn()
            content_type = "application/octet-stream"
            lower = object_name.lower()
            if lower.endswith('.png'):
                content_type = 'image/png'
            elif lower.endswith('.jpg') or lower.endswith('.jpeg'):
                content_type = 'image/jpeg'
            elif lower.endswith('.gif'):
                content_type = 'image/gif'
            elif lower.endswith('.webp'):
                content_type = 'image/webp'
            elif lower.endswith('.svg'):
                content_type = 'image/svg+xml'
            elif lower.endswith('.html'):
                content_type = 'text/html; charset=utf-8'
            elif lower.endswith('.json'):
                content_type = 'application/json; charset=utf-8'
            elif lower.endswith('.txt'):
                content_type = 'text/plain; charset=utf-8'
            return data, content_type
        except Exception as e:
            logger.error(f"下载字节内容失败: {e}")
            return None, None
    async def upload_image(self, message_id: int, image_bytes: bytes, filename: Optional[str] = None, mime_type: Optional[str] = None) -> Optional[str]:
        try:
            # 推断扩展名
            ext = ''
            if filename and '.' in filename:
                ext = filename[filename.rfind('.'):]
            else:
                # 尝试从 mime 推断
                if mime_type == 'image/png':
                    ext = '.png'
                elif mime_type in ['image/jpeg', 'image/jpg']:
                    ext = '.jpg'
                elif mime_type == 'image/gif':
                    ext = '.gif'
                elif mime_type == 'image/webp':
                    ext = '.webp'
                else:
                    ext = '.bin'
            object_name = self._generate_object_name(message_id, 'attachment', ext)
            try:
                self._ensure_bucket_exists()
            except Exception:
                pass
            ct = mime_type or 'application/octet-stream'
            result = self.client.put_object(
                self.bucket_name,
                object_name,
                io.BytesIO(image_bytes),
                len(image_bytes),
                content_type=ct
            )
            url = self.client.presigned_get_object(self.bucket_name, object_name, expires=timedelta(days=7))
            logger.info(f"✅ 图片上传成功: {url}")
            return url
        except Exception as e:
            logger.error(f"❌ 图片上传失败: {e}")
            return None



    async def delete_message_content(self, message_id: int) -> bool:
        """删除消息的所有内容文件"""
        try:
            # 列出消息相关的所有对象
            objects = self.client.list_objects(
                self.bucket_name,
                prefix=f"messages/{message_id}/",
                recursive=True
            )

            # 删除所有对象
            for obj in objects:
                self.client.remove_object(self.bucket_name, obj.object_name)
                logger.info(f"删除对象: {obj.object_name}")

            return True

        except Exception as e:
            logger.error(f"删除消息内容失败: {e}")
            return False

# 创建全局MinIO服务实例
minio_service = MinIOService()
