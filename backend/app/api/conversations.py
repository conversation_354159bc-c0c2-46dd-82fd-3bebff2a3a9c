# app/api/conversations.py - 对话API路由

import logging
import httpx
from fastapi import APIRouter, HTTPException, status, Depends
from datetime import datetime
from typing import List

from ..models.schemas import (
    ConversationCreate, ConversationInfo, ConversationsResponse,
    MessagesResponse, MessageInfo, BaseResponse, UserInfo
)
from pydantic import BaseModel
from ..api.auth import get_current_user_dependency
from ..core.config import settings

logger = logging.getLogger(__name__)
router = APIRouter()

# 开发环境后备存储（Record API 不可用时使用）
FAKE_CONVERSATIONS = {}
FAKE_CONV_ID_SEQ = 100000

# 更新对话标题的请求模型
class ConversationTitleUpdate(BaseModel):
    title: str

@router.get("/", response_model=ConversationsResponse)
async def get_conversations(current_user = Depends(get_current_user_dependency)):
    """
    获取用户的对话列表
    """
    try:
        logger.info(f"获取用户 {current_user.id} 的对话列表")
        try:
            async with httpx.AsyncClient() as client:
                headers = {"Authorization": f"Bearer {settings.RECORD_API_KEY}"}
                url = f"{settings.RECORD_API_BASE_URL}/api/v1/conversations/"
                params = {"user_id": current_user.id}
                logger.info(f"请求URL: {url}, 参数: {params}")
                response = await client.get(url, headers=headers, params=params, timeout=10.0)
                response.raise_for_status()
                conversations_data = response.json()
        except Exception as e:
            logger.warning(f"Record API 不可用，使用后备对话列表: {e}")
            # 后备：返回当前内存中的对话
            conversations_data = [v for v in FAKE_CONVERSATIONS.values() if v.get('user_id') == current_user.id]

        conversations = []
        for conv in conversations_data:
            if isinstance(conv, dict):
                try:
                    conversation = ConversationInfo(**conv)
                    conversations.append(conversation)
                except Exception as e:
                    logger.error(f"转换对话数据失败: {conv}, 错误: {e}")
                    raise

        logger.info(f"用户 {current_user.id} 获取到 {len(conversations)} 个对话")
        return ConversationsResponse(
            conversations=conversations,
            message=f"成功获取 {len(conversations)} 个对话"
        )
    except Exception as e:
        logger.error(f"获取对话列表异常: {e}")
        raise HTTPException(status_code=500, detail="获取对话列表服务内部错误")

@router.post("/")
async def create_conversation(
    conversation: ConversationCreate,
    current_user = Depends(get_current_user_dependency)
):
    """
    创建新对话
    """
    try:
        # 先尝试真实服务
        try:
            async with httpx.AsyncClient() as client:
                headers = {"Authorization": f"Bearer {settings.RECORD_API_KEY}"}
                data = {"user_id": current_user.id, "title": conversation.title}
                response = await client.post(
                    f"{settings.RECORD_API_BASE_URL}/api/v1/conversations/",
                    headers=headers,
                    json=data,
                    timeout=10.0
                )
                response.raise_for_status()
                conversation_data = response.json()
        except Exception as e:
            logger.warning(f"Record API 不可用，使用后备创建对话: {e}")
            global FAKE_CONV_ID_SEQ
            FAKE_CONV_ID_SEQ += 1
            conversation_data = {
                "id": FAKE_CONV_ID_SEQ,
                "title": conversation.title or "新对话",
                "created_at": datetime.utcnow().isoformat(),
                "latest_revised_at": datetime.utcnow().isoformat(),
                "user_id": current_user.id,
                "message_count": 0
            }
            FAKE_CONVERSATIONS[FAKE_CONV_ID_SEQ] = conversation_data

        conversation_info = ConversationInfo(**conversation_data)
        return {"success": True, "message": "对话创建成功", "conversation": conversation_info}
    except Exception as e:
        logger.error(f"创建对话异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="创建对话服务内部错误")


@router.delete("/{conversation_id}")
async def delete_conversation_basic(
    conversation_id: int,
    current_user = Depends(get_current_user_dependency)
):
    """
    删除对话
    """
    try:
        # 先尝试真实服务
        try:
            async with httpx.AsyncClient() as client:
                headers = {"Authorization": f"Bearer {settings.RECORD_API_KEY}"}
                response = await client.delete(
                    f"{settings.RECORD_API_BASE_URL}/api/v1/conversations/{conversation_id}",
                    headers=headers,
                    timeout=10.0
                )
                response.raise_for_status()
        except Exception as e:
            logger.warning(f"Record API 不可用，使用后备删除对话: {e}")
            if conversation_id in FAKE_CONVERSATIONS:
                del FAKE_CONVERSATIONS[conversation_id]
        return {"success": True, "message": "对话删除成功", "conversation_id": conversation_id}
    except Exception as e:
        logger.error(f"删除对话异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="删除对话服务内部错误")

@router.get("/{conversation_id}", response_model=ConversationInfo)
async def get_conversation(
    conversation_id: int,
    current_user = Depends(get_current_user_dependency)
):
    """
    获取特定对话信息
    """
    try:
        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {settings.RECORD_API_KEY}"}
            response = await client.get(
                f"{settings.RECORD_API_BASE_URL}/api/v1/conversations/{conversation_id}",
                headers=headers,
                timeout=10.0
            )

            if response.status_code == 200:
                conversation_data = response.json()
                return ConversationInfo(**conversation_data)
            elif response.status_code == 404:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="对话不存在"
                )
            else:
                logger.error(f"获取对话失败: HTTP {response.status_code}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="获取对话失败"
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取对话异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取对话服务内部错误"
        )

@router.get("/{conversation_id}/messages", response_model=MessagesResponse)
async def get_conversation_messages(
    conversation_id: int,
    current_user = Depends(get_current_user_dependency)
):
    """
    获取对话的消息列表
    """
    try:
        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {settings.RECORD_API_KEY}"}
            params = {"conversation_id": conversation_id}
            response = await client.get(
                f"{settings.RECORD_API_BASE_URL}/api/v1/messages/",
                headers=headers,
                params=params,
                timeout=10.0
            )

            if response.status_code == 200:
                messages_data = response.json()

                # 转换为标准格式
                messages = []
                for msg in messages_data:
                    if isinstance(msg, dict):
                        message = MessageInfo(**msg)
                        messages.append(message)

                logger.info(f"对话 {conversation_id} 获取到 {len(messages)} 条消息")
                return MessagesResponse(
                    messages=messages,
                    message=f"成功获取 {len(messages)} 条消息"
                )
            elif response.status_code == 404:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="对话不存在"
                )
            else:
                logger.error(f"获取消息失败: HTTP {response.status_code}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="获取消息失败"
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取消息异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取消息服务内部错误"
        )

@router.delete("/{conversation_id}", response_model=BaseResponse)
async def delete_conversation(
    conversation_id: int,
    current_user = Depends(get_current_user_dependency)
):
    """
    删除对话
    """
    try:
        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {current_user.api_key}"}
            response = await client.delete(
                f"{settings.RECORD_API_BASE_URL}/api/v1/conversations/{conversation_id}",
                headers=headers,
                timeout=10.0
            )

            if response.status_code == 200:
                logger.info(f"用户 {current_user.id} 删除对话 {conversation_id} 成功")
                return BaseResponse(message="对话删除成功")
            elif response.status_code == 404:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="对话不存在"
                )
            else:
                logger.error(f"删除对话失败: HTTP {response.status_code}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="删除对话失败"
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除对话异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除对话服务内部错误"
        )

@router.post("/{conversation_id}/set-current", response_model=BaseResponse)
async def set_current_conversation(
    conversation_id: int,
    current_user = Depends(get_current_user_dependency)
):
    """
    设置当前对话
    """
    try:
        # 首先验证对话是否存在且属于当前用户
        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {settings.RECORD_API_KEY}"}
            response = await client.get(
                f"{settings.RECORD_API_BASE_URL}/api/v1/conversations/{conversation_id}",
                headers=headers,
                timeout=10.0
            )

            if response.status_code == 404:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="对话不存在"
                )
            elif response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="对话服务暂时不可用"
                )

            conversation_data = response.json()
            if conversation_data.get("user_id") != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问此对话"
                )

        # 更新用户的current_conversation_id
        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {current_user.api_key}"}
            update_data = {"current_conversation_id": conversation_id}

            response = await client.patch(
                f"{settings.RECORD_API_BASE_URL}/api/v1/users/me/settings",
                headers=headers,
                json=update_data,
                timeout=10.0
            )

            if response.status_code == 200:
                logger.info(f"用户 {current_user.id} 当前对话设置为 {conversation_id}")
                return BaseResponse(message=f"当前对话已设置为: {conversation_data.get('title', '未知对话')}")
            else:
                logger.error(f"设置当前对话失败: HTTP {response.status_code}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="设置当前对话失败"
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"设置当前对话异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="设置当前对话服务内部错误"
        )

@router.post("/{conversation_id}/title", response_model=BaseResponse)
async def update_conversation_title(
    conversation_id: int,
    title_update: ConversationTitleUpdate,
    current_user = Depends(get_current_user_dependency)
):
    """
    更新对话标题
    """
    try:
        logger.info(f"更新对话 {conversation_id} 标题为: {title_update.title}")

        async with httpx.AsyncClient() as client:
            # 尝试使用记录服务管理密钥
            headers = {"Authorization": f"Bearer {settings.RECORD_API_KEY}"}
            data = {"title": title_update.title}

            response = await client.patch(
                f"{settings.RECORD_API_BASE_URL}/api/v1/conversations/{conversation_id}/title",
                headers=headers,
                json=data,
                timeout=10.0
            )

            if response.status_code == 200:
                logger.info(f"对话 {conversation_id} 标题更新成功")
                return BaseResponse(message="对话标题更新成功")
            elif response.status_code == 404:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="对话不存在"
                )
            else:
                # 回退1：尝试使用用户令牌 + PATCH
                logger.warning(f"管理密钥更新标题失败: HTTP {response.status_code}, body={response.text}")
                user_headers = {"Authorization": f"Bearer {current_user.api_key}"}
                user_data = {"title": title_update.title}
                try:
                    retry_resp = await client.patch(
                        f"{settings.RECORD_API_BASE_URL}/api/v1/conversations/{conversation_id}/title",
                        headers=user_headers,
                        json=user_data,
                        timeout=10.0
                    )
                    if retry_resp.status_code == 200:
                        logger.info(f"使用用户令牌(PATCH)更新标题成功: conv={conversation_id}")
                        return BaseResponse(message="对话标题更新成功")
                except Exception as re:
                    logger.warning(f"用户令牌(PATCH)更新标题异常: {re}")

                # 回退3：开发环境内存方案
                if conversation_id in FAKE_CONVERSATIONS:
                    FAKE_CONVERSATIONS[conversation_id]["title"] = title_update.title
                    logger.info(f"本地内存中已更新对话标题: conv={conversation_id}")
                    return BaseResponse(message="对话标题已更新(开发模式)")

                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="更新对话标题失败"
                )

    except httpx.TimeoutException:
        logger.error("更新对话标题请求超时")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="对话服务请求超时"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新对话标题异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新对话标题服务内部错误"
        )
