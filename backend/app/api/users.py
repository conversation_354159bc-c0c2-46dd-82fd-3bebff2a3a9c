# app/api/users.py - 用户API路由

import logging
import httpx
from fastapi import APIRouter, HTTPException, status, Depends

from ..models.schemas import UserInfo, UserSettings, SettingsResponse, BaseResponse
from ..api.auth import get_current_user_dependency
from ..core.config import settings

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/me", response_model=UserInfo)
async def get_user_info(current_user = Depends(get_current_user_dependency)):
    """
    获取当前用户信息
    """
    return current_user

@router.get("/balance")
async def get_user_balance(current_user = Depends(get_current_user_dependency)):
    """
    获取用户余额信息
    """
    try:
        return {
            "success": True,
            "user_id": current_user.id,
            "total_deposited": current_user.total_deposited,
            "total_spent": current_user.total_spent,
            "current_balance": current_user.current_balance,
            "total_prompt_tokens": current_user.total_prompt_tokens,
            "total_completion_tokens": current_user.total_completion_tokens,
            "message": "余额信息获取成功"
        }
    except Exception as e:
        logger.error(f"获取用户余额异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取余额信息服务内部错误"
        )

@router.get("/settings", response_model=SettingsResponse)
async def get_user_settings(current_user = Depends(get_current_user_dependency)):
    """
    获取用户设置
    """
    try:
        settings_data = UserSettings(
            current_model_id=current_user.current_model_id,
            current_temperature=float(current_user.current_temperature) if current_user.current_temperature else None,
            current_conversation_id=current_user.current_conversation_id,
            mathjax=current_user.mathjax
        )
        
        return SettingsResponse(
            settings=settings_data,
            message="用户设置获取成功"
        )
    except Exception as e:
        logger.error(f"获取用户设置异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户设置服务内部错误"
        )

@router.patch("/settings", response_model=SettingsResponse)
async def update_user_settings(
    settings_update: UserSettings,
    current_user = Depends(get_current_user_dependency)
):
    """
    更新用户设置
    """
    try:
        # 构建更新数据
        update_data = {}
        
        if settings_update.current_model_id is not None:
            update_data["current_model_id"] = settings_update.current_model_id

        if settings_update.current_temperature is not None:
            update_data["current_temperature"] = str(settings_update.current_temperature)

        if settings_update.current_conversation_id is not None:
            update_data["current_conversation_id"] = settings_update.current_conversation_id

        if settings_update.mathjax is not None:
            update_data["mathjax"] = settings_update.mathjax
        
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="没有提供要更新的设置"
            )
        
        # 调用Record API更新用户设置
        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {current_user.api_key}"}
            response = await client.patch(
                f"{settings.RECORD_API_BASE_URL}/api/v1/users/{current_user.id}",
                headers=headers,
                json=update_data,
                timeout=10.0
            )
            
            if response.status_code == 200:
                updated_user = response.json()
                
                # 返回更新后的设置
                updated_settings = UserSettings(
                    current_model_id=updated_user.get("current_model_id"),
                    current_temperature=float(updated_user.get("current_temperature", 0)) if updated_user.get("current_temperature") else None,
                    current_conversation_id=updated_user.get("current_conversation_id"),
                    mathjax=updated_user.get("mathjax")
                )
                
                logger.info(f"用户 {current_user.id} 设置更新成功")
                return SettingsResponse(
                    settings=updated_settings,
                    message="用户设置更新成功"
                )
            else:
                logger.error(f"更新用户设置失败: HTTP {response.status_code}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="更新用户设置失败"
                )
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户设置异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户设置服务内部错误"
        )

@router.get("/stats")
async def get_user_stats(current_user = Depends(get_current_user_dependency)):
    """
    获取用户统计信息
    """
    try:
        # 获取用户的对话统计
        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {current_user.api_key}"}
            
            # 获取对话列表
            conversations_response = await client.get(
                f"{settings.RECORD_API_BASE_URL}/api/v1/conversations",
                headers=headers,
                timeout=10.0
            )
            
            total_conversations = 0
            total_messages = 0
            
            if conversations_response.status_code == 200:
                conversations = conversations_response.json()
                total_conversations = len(conversations)
                
                # 计算总消息数
                for conv in conversations:
                    total_messages += conv.get("message_count", 0)
            
            return {
                "success": True,
                "user_id": current_user.id,
                "total_conversations": total_conversations,
                "total_messages": total_messages,
                "total_prompt_tokens": current_user.total_prompt_tokens,
                "total_completion_tokens": current_user.total_completion_tokens,
                "total_tokens": current_user.total_prompt_tokens + current_user.total_completion_tokens,
                "current_balance": current_user.current_balance,
                "total_spent": current_user.total_spent,
                "message": "用户统计信息获取成功"
            }
            
    except Exception as e:
        logger.error(f"获取用户统计异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户统计服务内部错误"
        )

@router.post("/refresh-info", response_model=UserInfo)
async def refresh_user_info(current_user = Depends(get_current_user_dependency)):
    """
    刷新用户信息（从Record API重新获取最新数据）
    """
    try:
        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {current_user.api_key}"}
            response = await client.get(
                f"{settings.RECORD_API_BASE_URL}/api/v1/users/me",
                headers=headers,
                timeout=10.0
            )
            
            if response.status_code == 200:
                user_data = response.json()
                updated_user = UserInfo(**user_data)
                logger.info(f"用户 {current_user.id} 信息刷新成功")
                return updated_user
            else:
                logger.error(f"刷新用户信息失败: HTTP {response.status_code}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="用户信息服务暂时不可用"
                )
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新用户信息异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新用户信息服务内部错误"
        )
