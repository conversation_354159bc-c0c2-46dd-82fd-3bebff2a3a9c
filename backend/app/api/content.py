# app/api/content.py - 内容代理API（用于代理获取 MinIO 预签名URL内容）

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import HTMLResponse, PlainTextResponse, Response, StreamingResponse
from ..services.minio_service import minio_service

router = APIRouter()

@router.get("/proxy")
async def proxy_content(url: str = Query(..., description="MinIO 预签名URL")):
    """
    代理获取 MinIO 预签名URL的内容，避免前端跨域问题。
    - HTML/JSON/TEXT 返回文本
    - 图片/二进制文件返回 StreamingResponse
    """
    try:
        # 优先尝试以文本方式下载（适用于HTML/JSON/TEXT）
        content = await minio_service.download_content_from_url(url)
        if content is not None:
            if ".html" in url:
                return HTMLResponse(content)
            elif ".json" in url:
                return Response(content, media_type="application/json")
            else:
                return PlainTextResponse(content)

        # 文本失败时，尝试二进制下载（适用于图片等静态资源）
        data, content_type = await minio_service.download_bytes_from_url(url)
        if data is None:
            raise HTTPException(status_code=404, detail="Content not found")
        return StreamingResponse(iter([data]), media_type=content_type)

    except HTTPException:
        raise
    except Exception:
        # 不暴露内部细节
        raise HTTPException(status_code=500, detail="Failed to fetch content")

