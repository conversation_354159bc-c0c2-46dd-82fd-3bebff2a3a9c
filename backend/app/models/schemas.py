# app/models/schemas.py - Pydantic数据模型

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

# 基础响应模型
class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = True
    message: str = "操作成功"
    timestamp: datetime = Field(default_factory=datetime.now)

class ErrorResponse(BaseResponse):
    """错误响应模型"""
    success: bool = False
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

# 用户相关模型
class UserLogin(BaseModel):
    """用户登录请求"""
    api_key: str = Field(..., min_length=1, description="用户API密钥")

class UserInfo(BaseModel):
    """用户信息"""
    id: int
    api_key: str
    is_active: bool
    permission: int
    mathjax: bool
    current_model_id: Optional[int] = None
    current_temperature: str
    current_conversation_id: Optional[int] = None
    total_deposited: str
    total_spent: str
    current_balance: str
    total_prompt_tokens: int
    total_completion_tokens: int
    created_at: datetime

class LoginResponse(BaseResponse):
    """登录响应"""
    access_token: str
    token_type: str = "bearer"
    user: UserInfo

# 模型相关
class ModelInfo(BaseModel):
    """模型信息"""
    id: str
    name: str
    display_name: Optional[str] = None
    description: Optional[str] = None
    max_tokens: Optional[int] = None
    supports_images: bool = False
    input_cost_per_token: Optional[float] = None
    output_cost_per_token: Optional[float] = None

class ModelsResponse(BaseResponse):
    """模型列表响应"""
    models: List[ModelInfo]

# 对话相关
class ConversationCreate(BaseModel):
    """创建对话请求"""
    title: str = Field(..., min_length=1, max_length=200, description="对话标题")

class ConversationInfo(BaseModel):
    """对话信息"""
    id: int
    title: str
    created_at: Union[datetime, str]
    latest_revised_at: Union[datetime, str]
    user_id: int
    message_count: Optional[int] = 0

    @validator('created_at', 'latest_revised_at', pre=True)
    @classmethod
    def parse_datetime(cls, v):
        if isinstance(v, str):
            try:
                return datetime.fromisoformat(v.replace('Z', '+00:00'))
            except ValueError:
                return datetime.fromisoformat(v)
        return v

    @validator('message_count', pre=True)
    @classmethod
    def parse_message_count(cls, v):
        return v if v is not None else 0

class ConversationsResponse(BaseResponse):
    """对话列表响应"""
    conversations: List[ConversationInfo]

# 消息相关
class MessageRole(str, Enum):
    """消息角色"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"

class MessageCreate(BaseModel):
    """创建消息请求"""
    conversation_id: int
    role: MessageRole
    content: str = Field(..., min_length=1, description="消息内容")
    model_name: Optional[str] = None
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0)
    max_tokens: Optional[int] = Field(None, gt=0)

class MessageInfo(BaseModel):
    """消息信息"""
    id: int
    conversation_id: int
    role: str
    created_at: datetime
    updated_at: datetime
    content_url: Optional[str] = None
    plain_html_url: Optional[str] = None
    model_id: Optional[int] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    is_error: bool = False
    error_info: Optional[str] = None
    cost: Optional[str] = None

class MessagesResponse(BaseResponse):
    """消息列表响应"""
    messages: List[MessageInfo]

# 聊天相关
class ChatRequest(BaseModel):
    """聊天请求"""
    conversation_id: int
    message: str = Field(..., min_length=1, description="用户消息")
    model_name: Optional[str] = None
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0)
    max_tokens: Optional[int] = Field(None, gt=0)
    stream: bool = False
    # 新增：临时图片ID列表（上传完成后得到），发送消息时将这些图片以base64内联进内容
    attachments: Optional[List[int]] = None

class ChatResponse(BaseResponse):
    """聊天响应"""
    conversation_id: int
    user_message_id: int
    assistant_message_id: Optional[int] = None
    response: Optional[str] = None
    error: Optional[str] = None
    user_message: Optional[Dict[str, Any]] = None
    assistant_message: Optional[Dict[str, Any]] = None
    usage: Optional[Dict[str, Any]] = None
    cost: Optional[float] = None


# 图片上传（聊天）
class ImageUploadRequest(BaseModel):
    conversation_id: int
    image_base64: str
    filename: Optional[str] = None
    caption: Optional[str] = None
    mime_type: Optional[str] = None

class ImageUploadResponse(BaseResponse):
    conversation_id: int
    user_message_id: int

# 临时图片存储（仅base64）
class TempImageUploadRequest(BaseModel):
    image_base64: str
    filename: Optional[str] = None
    mime_type: Optional[str] = None

class TempImageInfo(BaseModel):
    id: int
    filename: Optional[str] = None
    mime_type: Optional[str] = None
    size: int

class TempImageUploadResponse(BaseResponse):
    image: TempImageInfo

    user_message: Optional[Dict[str, Any]] = None

# 文件上传
class FileUploadResponse(BaseResponse):
    """文件上传响应"""
    file_url: str
    file_name: str
    file_size: int
    content_type: str

# 设置相关
class UserSettings(BaseModel):
    """用户设置"""
    current_model_id: Optional[int] = None
    current_temperature: Optional[float] = Field(None, ge=0.0, le=2.0)
    current_conversation_id: Optional[int] = None
    mathjax: Optional[bool] = None

class SettingsResponse(BaseResponse):
    """设置响应"""
    settings: UserSettings

# WebSocket消息
class WSMessageType(str, Enum):
    """WebSocket消息类型"""
    CHAT_START = "chat_start"
    CHAT_CHUNK = "chat_chunk"
    CHAT_END = "chat_end"
    CHAT_ERROR = "chat_error"
    STATUS_UPDATE = "status_update"

class WSMessage(BaseModel):
    """WebSocket消息"""
    type: WSMessageType
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.now)
