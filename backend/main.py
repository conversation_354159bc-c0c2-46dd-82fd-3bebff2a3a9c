#!/usr/bin/env python3
# main.py - FastAPI应用入口

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import uvicorn
import logging

from app.api import auth, chat, conversations, models, users, content
from app.core.config import settings
from app.core.logging_config import setup_logging

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("🚀 InspirFlow FastAPI 启动中...")
    logger.info(f"📍 Model API: {settings.MODEL_API_BASE_URL}")
    logger.info(f"📍 Record API: {settings.RECORD_API_BASE_URL}")
    logger.info(f"📍 MinIO: {settings.MINIO_ENDPOINT}")
    yield
    logger.info("🛑 InspirFlow FastAPI 关闭")

# 创建FastAPI应用
app = FastAPI(
    title="InspirFlow API",
    description="现代化聊天应用后端API",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(auth.router, prefix="/api/v1/auth", tags=["认证"])
app.include_router(users.router, prefix="/api/v1/users", tags=["用户"])
app.include_router(models.router, prefix="/api/v1/models", tags=["模型"])
app.include_router(conversations.router, prefix="/api/v1/conversations", tags=["对话"])
app.include_router(chat.router, prefix="/api/v1/chat", tags=["聊天"])
app.include_router(content.router, prefix="/api/v1/content", tags=["内容"])

# 静态文件服务（用于开发环境）
if settings.ENVIRONMENT == "development":
    import os
    static_dir = "static"
    if os.path.exists(static_dir):
        app.mount("/static", StaticFiles(directory=static_dir), name="static")

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "InspirFlow FastAPI Backend",
        "version": "2.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "timestamp": "2025-07-31T08:00:00Z"
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.ENVIRONMENT == "development",
        log_level="info"
    )
