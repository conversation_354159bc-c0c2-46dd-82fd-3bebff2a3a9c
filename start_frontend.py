#!/usr/bin/env python3
"""
InspirFlow Vue.js 前端启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """启动Vue.js前端应用"""
    
    project_root = Path(__file__).parent
    frontend_dir = project_root / "frontend"
    
    if not frontend_dir.exists():
        print("❌ 前端目录不存在")
        sys.exit(1)
    
    # 切换到frontend目录
    os.chdir(frontend_dir)
    
    print(f"🚀 启动 InspirFlow Vue.js 前端应用")
    print(f"📍 目录: {frontend_dir}")
    print(f"🌐 地址: http://localhost:3000")
    print("-" * 50)
    
    # 检查是否安装了依赖
    node_modules = frontend_dir / "node_modules"
    if not node_modules.exists():
        print("📦 检测到未安装依赖，正在安装...")
        try:
            subprocess.run(["npm", "install"], check=True)
            print("✅ 依赖安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖安装失败，请手动运行 'npm install'")
            sys.exit(1)
        except FileNotFoundError:
            print("❌ 未找到 npm 命令，请确保已安装 Node.js")
            sys.exit(1)
    
    # 启动开发服务器
    try:
        subprocess.run(["npm", "run", "dev"], check=True)
    except subprocess.CalledProcessError:
        print("❌ 前端启动失败")
        sys.exit(1)
    except FileNotFoundError:
        print("❌ 未找到 npm 命令，请确保已安装 Node.js")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 前端服务已停止")

if __name__ == "__main__":
    main()
