# InspirFlow 部署指南

本指南介绍如何使用提供的脚本来构建前端和部署 InspirFlow 应用。

## ✅ 部署状态

**当前部署状态：** 🎉 **成功部署并运行中**

- ✅ 前端服务：http://localhost:20020 (正常运行)
- ✅ 后端服务：http://localhost:20010 (正常运行)
- ✅ 健康检查：http://localhost:20010/health (通过)
- ✅ API 文档：http://localhost:20010/docs (可访问)

## 📋 前置要求

### 系统要求
- Linux/macOS 系统（推荐）
- Bash shell 支持

### 软件依赖
- **Node.js** >= 16.0.0
- **npm** >= 8.0.0
- **Docker** >= 20.10.0
- **Docker Compose** v2（推荐）或 v1

### 检查依赖
```bash
# 检查 Node.js 和 npm
node --version
npm --version

# 检查 Docker 和 Docker Compose
docker --version
docker compose version  # v2
# 或
docker-compose version   # v1
```

## 🚀 快速开始

### 方式一：一键部署（推荐）
```bash
# 在项目根目录执行
./one-click-deploy.sh
```

这个脚本会自动：
1. 构建前端应用
2. 检查并处理端口占用
3. 使用 Docker Compose 部署服务

### 方式二：分步执行

#### 1. 构建前端
```bash
./build-frontend.sh
```

#### 2. 部署应用
```bash
./deploy.sh
```

## 📁 脚本说明

### `build-frontend.sh` - 前端构建脚本
**功能：**
- 检查 Node.js 和 npm 环境
- 清理旧的构建文件
- 安装前端依赖
- 加载环境变量
- 构建生产版本的前端应用
- 验证构建结果

**使用方法：**
```bash
./build-frontend.sh
```

**输出：**
- 构建文件位于 `frontend/dist/` 目录

### `deploy.sh` - Docker Compose 部署脚本
**功能：**
- 检查 Docker 和 Docker Compose 环境
- 加载环境变量配置
- 检查并处理端口占用
- 自动构建前端（如果需要）
- 停止现有服务
- 构建 Docker 镜像
- 启动服务
- 检查服务状态

**使用方法：**
```bash
./deploy.sh
```

### `one-click-deploy.sh` - 一键部署脚本
**功能：**
- 整合前端构建和应用部署流程
- 提供友好的用户界面
- 显示详细的部署信息

**使用方法：**
```bash
./one-click-deploy.sh
```

### `deployment-summary.sh` - 部署状态总结脚本
**功能：**
- 检查所有服务的运行状态
- 显示访问地址和管理命令
- 提供故障排除指南
- 显示系统信息和下一步建议

**使用方法：**
```bash
./deployment-summary.sh
```

### `test-deployment.sh` - 部署环境测试脚本
**功能：**
- 测试部署环境的完整性
- 验证所有依赖是否正确安装
- 检查配置文件和脚本权限

**使用方法：**
```bash
./test-deployment.sh
```

## ⚙️ 配置说明

### 环境变量配置
确保项目根目录有 `.env` 文件，包含必要的配置：

```bash
# 复制示例配置文件
cp .env.example .env

# 编辑配置文件
vim .env  # 或使用其他编辑器
```

### 关键配置项
- `BACKEND_PORT`: 后端服务端口（默认：20010）
- `FRONTEND_PORT`: 前端服务端口（默认：20020）
- `VITE_API_BASE_URL`: 前端 API 基础 URL
- `ENVIRONMENT`: 运行环境（production/development）

## 🔧 端口管理

### 端口占用检查
脚本会自动检查配置的端口是否被占用，如果发现占用会：
1. 显示占用进程信息
2. 询问是否终止占用进程
3. 尝试优雅终止，失败则强制终止

### 手动处理端口占用
```bash
# 查看端口占用
lsof -i :20010  # 后端端口
lsof -i :20020  # 前端端口

# 终止占用进程
kill <PID>      # 优雅终止
kill -9 <PID>   # 强制终止
```

## 📊 服务管理

### 查看服务状态
```bash
docker compose ps
```

### 查看服务日志
```bash
# 查看所有服务日志
docker compose logs -f

# 查看特定服务日志
docker compose logs -f backend
docker compose logs -f frontend
```

### 重启服务
```bash
# 重启所有服务
docker compose restart

# 重启特定服务
docker compose restart backend
docker compose restart frontend
```

### 停止服务
```bash
docker compose down
```

### 完全清理
```bash
# 停止服务并删除容器、网络
docker compose down

# 删除镜像
docker compose down --rmi all

# 删除数据卷
docker compose down -v
```

## 🌐 访问应用

部署成功后，可以通过以下地址访问：

- **前端应用**: http://localhost:20020
- **后端 API**: http://localhost:20010
- **健康检查**: http://localhost:20010/health

## 🐛 故障排除

### 常见问题

#### 1. 端口被占用
**现象**: 部署时提示端口被占用
**解决**: 脚本会自动处理，或手动终止占用进程

#### 2. Docker 服务未启动
**现象**: 提示 Docker 服务不可用
**解决**: 启动 Docker 服务
```bash
sudo systemctl start docker  # Linux
# 或启动 Docker Desktop (macOS/Windows)
```

#### 3. 前端构建失败
**现象**: npm 构建过程中出错
**解决**: 
- 检查 Node.js 版本是否符合要求
- 清理 node_modules 重新安装依赖
- 检查网络连接

#### 4. 容器启动失败
**现象**: Docker 容器无法启动
**解决**:
- 查看容器日志：`docker compose logs [service_name]`
- 检查环境变量配置
- 确认镜像构建成功

### 日志查看
```bash
# 实时查看所有日志
docker compose logs -f

# 查看最近的日志
docker compose logs --tail=100

# 查看特定时间的日志
docker compose logs --since="2024-01-01T00:00:00"
```

### 性能监控
```bash
# 查看容器资源使用情况
docker stats

# 查看容器详细信息
docker compose ps -a
```

## 📝 开发模式

如果需要在开发模式下运行：

```bash
# 使用开发配置
docker compose -f docker-compose.dev.yml up -d

# 或直接运行开发服务器
cd frontend && npm run dev
cd backend && python main.py
```

## 🔄 更新部署

当代码更新后，重新部署：

```bash
# 方式一：使用一键部署脚本
./one-click-deploy.sh

# 方式二：分步更新
./build-frontend.sh  # 重新构建前端
./deploy.sh          # 重新部署
```

## 📞 技术支持

如果遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查服务日志
3. 确认环境配置正确
4. 联系技术支持团队
