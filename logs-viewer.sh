#!/bin/bash

# 日志查看脚本
# 用于方便地查看和管理 InspirFlow 服务日志

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示标题
show_header() {
    echo ""
    echo -e "${PURPLE}=================================="
    echo -e "    InspirFlow 日志查看器"
    echo -e "==================================${NC}"
    echo ""
}

# 检查 Docker Compose
check_docker_compose() {
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    elif command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    else
        log_error "Docker Compose 未安装"
        exit 1
    fi
}

# 显示服务状态
show_services() {
    log_info "当前服务状态："
    echo ""
    $COMPOSE_CMD ps
    echo ""
}

# 显示菜单
show_menu() {
    echo -e "${CYAN}请选择要查看的日志：${NC}"
    echo ""
    echo "  1) 📱 前端服务日志 (frontend)"
    echo "  2) 🔧 后端服务日志 (backend)"
    echo "  3) 📊 所有服务日志"
    echo "  4) 🔄 实时跟踪所有日志"
    echo "  5) 🔍 搜索日志内容"
    echo "  6) 📝 导出日志到文件"
    echo "  7) 🧹 清理日志"
    echo "  8) ❌ 退出"
    echo ""
}

# 查看前端日志
view_frontend_logs() {
    echo ""
    log_info "查看前端服务日志..."
    echo ""
    
    read -p "显示最近多少行日志？(默认50): " lines
    lines=${lines:-50}
    
    echo ""
    echo -e "${GREEN}=== 前端服务日志 (最近 $lines 行) ===${NC}"
    $COMPOSE_CMD logs --tail=$lines frontend
}

# 查看后端日志
view_backend_logs() {
    echo ""
    log_info "查看后端服务日志..."
    echo ""
    
    read -p "显示最近多少行日志？(默认50): " lines
    lines=${lines:-50}
    
    echo ""
    echo -e "${GREEN}=== 后端服务日志 (最近 $lines 行) ===${NC}"
    $COMPOSE_CMD logs --tail=$lines backend
}

# 查看所有日志
view_all_logs() {
    echo ""
    log_info "查看所有服务日志..."
    echo ""
    
    read -p "显示最近多少行日志？(默认50): " lines
    lines=${lines:-50}
    
    echo ""
    echo -e "${GREEN}=== 所有服务日志 (最近 $lines 行) ===${NC}"
    $COMPOSE_CMD logs --tail=$lines
}

# 实时跟踪日志
follow_logs() {
    echo ""
    log_info "开始实时跟踪日志..."
    log_warning "按 Ctrl+C 停止跟踪"
    echo ""
    
    echo -e "${GREEN}=== 实时日志跟踪 ===${NC}"
    $COMPOSE_CMD logs -f
}

# 搜索日志
search_logs() {
    echo ""
    log_info "搜索日志内容..."
    echo ""
    
    read -p "请输入搜索关键词: " keyword
    if [ -z "$keyword" ]; then
        log_error "搜索关键词不能为空"
        return 1
    fi
    
    read -p "搜索哪个服务？(frontend/backend/all，默认all): " service
    service=${service:-all}
    
    echo ""
    echo -e "${GREEN}=== 搜索结果: '$keyword' ===${NC}"
    
    case $service in
        "frontend")
            $COMPOSE_CMD logs frontend | grep -i "$keyword" --color=always
            ;;
        "backend")
            $COMPOSE_CMD logs backend | grep -i "$keyword" --color=always
            ;;
        "all"|*)
            $COMPOSE_CMD logs | grep -i "$keyword" --color=always
            ;;
    esac
    
    echo ""
    log_info "搜索完成"
}

# 导出日志
export_logs() {
    echo ""
    log_info "导出日志到文件..."
    echo ""
    
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local log_dir="logs_export"
    
    # 创建日志导出目录
    mkdir -p "$log_dir"
    
    read -p "导出哪个服务的日志？(frontend/backend/all，默认all): " service
    service=${service:-all}
    
    case $service in
        "frontend")
            local filename="$log_dir/frontend_logs_$timestamp.log"
            $COMPOSE_CMD logs frontend > "$filename"
            log_success "前端日志已导出到: $filename"
            ;;
        "backend")
            local filename="$log_dir/backend_logs_$timestamp.log"
            $COMPOSE_CMD logs backend > "$filename"
            log_success "后端日志已导出到: $filename"
            ;;
        "all"|*)
            local filename="$log_dir/all_logs_$timestamp.log"
            $COMPOSE_CMD logs > "$filename"
            log_success "所有日志已导出到: $filename"
            ;;
    esac
    
    echo ""
    log_info "导出的日志文件位于 $log_dir 目录"
}

# 清理日志
cleanup_logs() {
    echo ""
    log_warning "清理日志操作"
    echo ""
    
    echo "可选的清理操作："
    echo "  1) 清理 Docker 容器日志"
    echo "  2) 清理导出的日志文件"
    echo "  3) 两者都清理"
    echo "  4) 取消"
    echo ""
    
    read -p "请选择 (1-4): " choice
    
    case $choice in
        1)
            log_info "清理 Docker 容器日志..."
            echo ""
            log_warning "这将清理所有 Docker 容器的日志，确认继续？"
            read -p "(y/N): " -n 1 -r
            echo
            
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                # 重启容器以清理日志
                log_info "重启容器以清理日志..."
                $COMPOSE_CMD restart
                log_success "容器日志已清理"
            else
                log_info "操作已取消"
            fi
            ;;
        2)
            if [ -d "logs_export" ]; then
                log_info "清理导出的日志文件..."
                rm -rf logs_export
                log_success "导出的日志文件已清理"
            else
                log_info "没有找到导出的日志文件"
            fi
            ;;
        3)
            log_warning "这将清理所有日志，确认继续？"
            read -p "(y/N): " -n 1 -r
            echo
            
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                # 清理容器日志
                log_info "重启容器以清理日志..."
                $COMPOSE_CMD restart
                
                # 清理导出的日志
                if [ -d "logs_export" ]; then
                    rm -rf logs_export
                fi
                
                log_success "所有日志已清理"
            else
                log_info "操作已取消"
            fi
            ;;
        4|*)
            log_info "操作已取消"
            ;;
    esac
}

# 主循环
main_loop() {
    while true; do
        show_menu
        read -p "请选择 (1-8): " choice
        
        case $choice in
            1)
                view_frontend_logs
                ;;
            2)
                view_backend_logs
                ;;
            3)
                view_all_logs
                ;;
            4)
                follow_logs
                ;;
            5)
                search_logs
                ;;
            6)
                export_logs
                ;;
            7)
                cleanup_logs
                ;;
            8)
                log_info "退出日志查看器"
                exit 0
                ;;
            *)
                log_error "无效选择，请输入 1-8"
                ;;
        esac
        
        echo ""
        read -p "按 Enter 键继续..."
        clear
        show_header
        show_services
    done
}

# 主函数
main() {
    # 检查是否在项目根目录
    if [ ! -f "docker-compose.yml" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    check_docker_compose
    
    # 清屏并显示标题
    clear
    show_header
    show_services
    
    # 进入主循环
    main_loop
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
