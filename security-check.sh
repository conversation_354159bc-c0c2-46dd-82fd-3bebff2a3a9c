#!/bin/bash

# InspirFlow 安全检查脚本
# 用于检查项目中的安全配置和敏感信息

echo "🔒 InspirFlow 安全检查开始..."
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查结果计数
WARNINGS=0
ERRORS=0
PASSED=0

# 检查函数
check_pass() {
    echo -e "${GREEN}✅ PASS${NC}: $1"
    ((PASSED++))
}

check_warning() {
    echo -e "${YELLOW}⚠️  WARN${NC}: $1"
    ((WARNINGS++))
}

check_error() {
    echo -e "${RED}❌ ERROR${NC}: $1"
    ((ERRORS++))
}

echo "1. 检查环境变量文件..."
echo "--------------------------------"

# 检查 .env 文件是否存在
if [ -f ".env" ]; then
    check_pass ".env 文件存在"
    
    # 检查是否包含默认的不安全密钥
    if grep -q "your-super-secret-jwt-key-change-in-production" .env; then
        check_error ".env 文件包含默认的 JWT 密钥，请更换为强密钥"
    else
        check_pass "JWT 密钥已更换"
    fi
    
    if grep -q "admin-api-key-change-in-production" .env; then
        check_error ".env 文件包含默认的 API 密钥，请更换"
    else
        check_pass "API 密钥已更换"
    fi
    
    if grep -q "Rw80827mn@" .env; then
        check_warning ".env 文件包含示例 MinIO 密钥，建议更换"
    else
        check_pass "MinIO 密钥已更换"
    fi
    
else
    check_error ".env 文件不存在，请从 .env.example 复制并配置"
fi

echo ""
echo "2. 检查 .gitignore 配置..."
echo "--------------------------------"

if [ -f ".gitignore" ]; then
    if grep -q "\.env" .gitignore; then
        check_pass ".env 文件已添加到 .gitignore"
    else
        check_error ".env 文件未添加到 .gitignore，存在泄露风险"
    fi
else
    check_error ".gitignore 文件不存在"
fi

echo ""
echo "3. 检查源代码中的硬编码密钥..."
echo "--------------------------------"

# 检查后端配置文件
if grep -r "sk-OgYtY6BiFrjS2Mhcf_fObEF5u6dS2pKnysODXB7F2qQ" backend/app/ 2>/dev/null; then
    check_error "后端代码中发现硬编码的 API 密钥"
else
    check_pass "后端代码中未发现硬编码的 API 密钥"
fi

if grep -r "admin-api-key-change-in-production" backend/app/ 2>/dev/null; then
    check_error "后端代码中发现硬编码的默认密钥"
else
    check_pass "后端代码中未发现硬编码的默认密钥"
fi

# 检查前端代码
if grep -r "admin-api-key-change-in-production" frontend/src/ 2>/dev/null; then
    check_error "前端代码中发现硬编码的默认密钥"
else
    check_pass "前端代码中未发现硬编码的默认密钥"
fi

echo ""
echo "4. 检查文件权限..."
echo "--------------------------------"

if [ -f ".env" ]; then
    ENV_PERMS=$(stat -c "%a" .env 2>/dev/null || stat -f "%A" .env 2>/dev/null)
    if [ "$ENV_PERMS" = "600" ] || [ "$ENV_PERMS" = "0600" ]; then
        check_pass ".env 文件权限设置正确 ($ENV_PERMS)"
    else
        check_warning ".env 文件权限过于宽松 ($ENV_PERMS)，建议设置为 600"
        echo "  运行: chmod 600 .env"
    fi
fi

echo ""
echo "5. 检查 Docker 配置..."
echo "--------------------------------"

if [ -f "docker-compose.yml" ]; then
    check_pass "Docker Compose 配置文件存在"
    
    # 检查是否使用环境变量
    if grep -q "\${" docker-compose.yml; then
        check_pass "Docker Compose 使用环境变量配置"
    else
        check_warning "Docker Compose 可能未使用环境变量"
    fi
else
    check_warning "Docker Compose 配置文件不存在"
fi

if [ -f ".dockerignore" ]; then
    if grep -q "\.env" .dockerignore; then
        check_pass ".env 文件已添加到 .dockerignore"
    else
        check_error ".env 文件未添加到 .dockerignore"
    fi
else
    check_warning ".dockerignore 文件不存在"
fi

echo ""
echo "6. 检查生产环境配置..."
echo "--------------------------------"

if [ -f ".env" ]; then
    if grep -q "ENVIRONMENT=production" .env; then
        check_pass "环境设置为生产模式"
        
        if grep -q "DEBUG=false" .env; then
            check_pass "调试模式已关闭"
        else
            check_warning "生产环境建议关闭调试模式"
        fi
    else
        check_warning "环境未设置为生产模式"
    fi
fi

echo ""
echo "=================================="
echo "🔒 安全检查完成"
echo "=================================="
echo -e "通过: ${GREEN}$PASSED${NC}"
echo -e "警告: ${YELLOW}$WARNINGS${NC}"
echo -e "错误: ${RED}$ERRORS${NC}"

if [ $ERRORS -gt 0 ]; then
    echo ""
    echo -e "${RED}❌ 发现安全问题，请修复后再部署！${NC}"
    exit 1
elif [ $WARNINGS -gt 0 ]; then
    echo ""
    echo -e "${YELLOW}⚠️  发现安全警告，建议修复后部署。${NC}"
    exit 0
else
    echo ""
    echo -e "${GREEN}✅ 安全检查通过，可以安全部署！${NC}"
    exit 0
fi
