<template>
  <div class="chat-container">
    <!-- 侧边栏 -->
    <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <h2 v-if="!sidebarCollapsed">InspirFlow</h2>
        <el-button
          :icon="sidebarCollapsed ? Expand : Fold"
          circle
          size="small"
          @click="toggleSidebar"
        />
      </div>

      <!-- 对话列表 -->
      <div class="conversations-section">
        <div class="section-title" v-if="!sidebarCollapsed">
          <span>对话列表</span>
          <el-button :icon="Plus" size="small" @click="createNewConversation">
            新建对话
          </el-button>
        </div>
        <div class="conversations-list">
          <!-- 对话列表项 -->
          <div
            v-for="conv in conversations"
            :key="conv.id"
            class="conversation-item"
            :class="{ active: currentConversationId === conv.id }"
          >
            <div class="conversation-content" @click="selectConversation(conv.id)">
              <el-icon><ChatDotRound /></el-icon>
              <span v-if="!sidebarCollapsed" class="conversation-title">
                {{ conv.title || '新对话' }}
              </span>
            </div>
            <el-button
              v-if="!sidebarCollapsed"
              :icon="Delete"
              size="small"
              type="danger"
              text
              class="delete-btn"
              @click.stop="deleteConversation(conv.id)"
            />
          </div>
        </div>
      </div>

      <!-- 模型设置 -->
      <div class="model-section" v-if="!sidebarCollapsed">
        <div class="section-title">模型设置</div>
        <el-select v-model="selectedModel" placeholder="选择模型" size="small">
          <el-option
            v-for="model in models"
            :key="model.id"
            :label="model.name"
            :value="model.id"
          />
        </el-select>

        <div class="temperature-setting">
          <label>温度: {{ temperature }}</label>
          <el-slider
            v-model="temperature"
            :min="0"
            :max="2"
            :step="0.1"
            size="small"
          />
        </div>

        <!-- LaTeX渲染设置 -->
        <div class="latex-setting">
          <div class="setting-row">
            <label>LaTeX渲染</label>
            <el-switch
              v-model="latexRenderEnabled"
              size="small"
              @change="onLatexRenderToggle"
            />
          </div>
        </div>
      </div>

      <!-- 用户信息 -->
      <div class="user-section" v-if="!sidebarCollapsed">
        <div class="user-info">
          <div class="user-name">{{ userInfo?.username || '用户' }}</div>
          <div class="user-balance">余额: ¥{{ userInfo?.current_balance || 0 }}</div>
        </div>
        <el-button size="small" @click="logout">登出</el-button>
      </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="main-content">
      <!-- 聊天消息区域 -->
      <div class="chat-messages" ref="messagesContainer">
        <div
          v-for="message in messages"
          :key="message.id"
          class="message-wrapper"
          :class="message.role"
        >
          <div class="message-content">
            <div class="message-header">
              <span class="message-role">
                {{ message.role === 'user' ? '用户' : 'AI助手' }}
              </span>
              <span class="message-time">
                {{ formatTime(message.created_at) }}
              </span>
            </div>

            <!-- 使用iframe显示消息内容 -->
            <div class="message-body">
              <!-- 显示错误信息 -->
              <div v-if="message.is_error" class="message-error">
                <el-icon><Warning /></el-icon>
                <span>{{ message.error_info || '消息处理出错' }}</span>
              </div>
              <!-- 显示正常消息 -->
              <div
                v-else-if="getMessageDisplayUrl(message)"
                class="message-html-content"
                @click="onMessageHtmlClick"
                v-html="processMessageContentWithCodeBlocks(getMessageContent(message))"
              />
              <!-- 后备显示 -->
              <div v-else class="message-text">
                {{ message.content || '消息加载中...' }}
              </div>


            </div>

            <!-- 消息操作按钮 -->
            <div class="message-actions">
              <el-button
                :icon="Delete"
                size="small"
                type="danger"
                text
                class="delete-message-btn"
                @click="deleteMessage(message.id)"
                title="删除消息"
              />
            </div>
          </div>
        </div>

        <!-- 加载指示器 -->
        <div v-if="isGenerating" class="generating-indicator">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>AI正在思考中...</span>
        </div>
      </div>

      <!-- 状态栏 -->
      <div class="status-bar">
        <div class="status-info">
          <span>状态: {{ isGenerating ? '生成中' : '就绪' }}</span>
          <span>模型: {{ selectedModelName }}</span>
          <span v-if="lastUsageTokens">本次: {{ lastUsageTokens.prompt }}/{{ lastUsageTokens.completion }} tokens</span>
          <span v-if="lastCostYuan">费用: ¥{{ lastCostYuan }}</span>
          <span v-if="lastCostBreakdown" style="opacity:0.85;">{{ lastCostBreakdown }}</span>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-area">
        <el-input
          v-model="inputMessage"
          type="textarea"
          :rows="3"
          placeholder="输入您的消息..."
          :disabled="isGenerating"
          @keydown.ctrl.enter="sendMessage"
        />
        <!-- 待发送的图片预览区 -->
        <div v-if="pendingPreviews.length" class="pending-attachments">
          <div v-for="p in pendingPreviews" :key="p.id" class="pending-thumb">
            <img :src="p.url" :alt="p.name" @click="imagePreviewList = [p.url]; imageViewerVisible = true" />
            <span class="name">{{ p.name }}</span>
            <el-button size="small" text type="danger" @click="removePendingAttachment(p.id)">移除</el-button>
          </div>
        </div>
        <div class="input-actions">
        <!-- 上传进度条 -->
        <div v-if="uploadingImage" class="upload-progress">
          <el-progress :percentage="uploadProgress" :stroke-width="6" :text-inside="true" status="success" />
        </div>
          <el-upload
            accept="image/*"
            :show-file-list="false"
            :auto-upload="false"
            @change="onUploadChange"
          >
            <el-button type="default">上传图片</el-button>
          </el-upload>
          <el-button
            type="primary"
            :loading="isGenerating"

            :disabled="!inputMessage.trim()"
            @click="sendMessage"
          >
            发送 (Ctrl+Enter)
          </el-button>
        </div>
      </div>
    </div>
    <!-- 图片预览查看器（模板底部，避免破坏布局） -->
    <el-image-viewer
      v-if="imageViewerVisible"
      :url-list="imagePreviewList"
      :initial-index="imagePreviewIndex"
      @close="imageViewerVisible = false"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import { get, post } from '@/api'
import { ElMessage, ElMessageBox } from 'element-plus'
import katex from 'katex'
import 'katex/dist/katex.min.css'
// 导入highlight.js用于代码语法高亮
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css' // 使用GitHub Light主题
import {
  ChatDotRound, Plus, Expand, Fold, Loading, Warning, Delete
} from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

const removePendingAttachment = (id) => {
  // 移除本地预览与附件ID
  pendingPreviews.value = pendingPreviews.value.filter(p => p.id !== id)
  pendingAttachments.value = pendingAttachments.value.filter(x => x !== id)
}


// 响应式数据
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)
const userInfo = computed(() => authStore.userInfo)

const conversations = ref([])
const models = ref([])
const messages = ref([])
const uploadingImage = ref(false)
const uploadProgress = ref(0)
const pendingAttachments = ref([]) // 临时图片ID列表，发送消息时带上
const pendingPreviews = ref([]) // 本地预览数据列表

const currentConversationId = ref(null)
// 选择图片回调（el-upload change 钩子）
const onUploadChange = async (uploadFile) => {
  const file = uploadFile.raw || uploadFile

  console.log('🖼️ 开始处理图片上传:', file.name, file.size, file.type)
  try {
    // 本地预览
    console.log('📄 转换文件为base64...')
    const base64 = await fileToBase64(file)
    console.log('✅ base64转换完成，长度:', base64.length)

    // 进度条：使用全局 loading 或局部进度（这里用ElMessage提示+状态）
    uploadingImage.value = true
    uploadProgress.value = 0
    console.log('📊 开始上传，设置进度状态')

    // 调用临时上传接口，仅存后端（base64）
    const payload = { image_base64: base64, filename: file.name, mime_type: file.type }
    console.log('🚀 发送上传请求到 /api/v1/chat/upload-temp-image')
    const resp = await post('/api/v1/chat/upload-temp-image', payload, {
      onUploadProgress: (e) => {
        // 注意：onUploadProgress 只有在浏览器直传时有效，当前是JSON请求显示近似进度
        if (e.total) {
          uploadProgress.value = Math.round((e.loaded * 100) / e.total)
          console.log('📈 上传进度:', uploadProgress.value + '%')
        }
      }
    })
    console.log('📥 上传响应:', resp)

    if (resp && resp.success && resp.image?.id) {
      // 记录临时图片ID，待发送消息时一并提交
      pendingAttachments.value.push(resp.image.id)
      console.log('📎 添加到待发送附件:', resp.image.id, '当前附件列表:', pendingAttachments.value)

      // 本地预览列表（使用 dataURL 预览）
      pendingPreviews.value.push({ id: resp.image.id, url: base64, name: file.name })
      console.log('🖼️ 添加到预览列表，当前预览数量:', pendingPreviews.value.length)

      ElMessage.success('图片上传成功，可继续编辑消息后发送')
      // 在输入框中插入一个占位提示（不嵌入base64，避免太大）
      inputMessage.value = (inputMessage.value || '') + `\n[已添加图片: ${file.name}]\n`
      console.log('✅ 图片上传处理完成')
    } else {
      console.error('❌ 上传失败，响应:', resp)
      ElMessage.error(resp?.message || '图片上传失败')
    }
  } catch (e) {
    console.error('❌ 上传图片异常:', e)
    ElMessage.error('图片上传失败')
  } finally {
    uploadingImage.value = false
    console.log('🏁 上传处理结束，重置状态')
  }
  return false // 阻止 el-upload 默认上传
}

// 图片预览相关
const imageViewerVisible = ref(false)
const imagePreviewList = ref([])
const imagePreviewIndex = ref(0)

const openImageViewer = (url) => {
  imagePreviewList.value = [url]
  imagePreviewIndex.value = 0
  imageViewerVisible.value = true
}

const fileToBase64 = (file) => new Promise((resolve, reject) => {
  const reader = new FileReader()
  reader.onload = () => resolve(reader.result)
  reader.onerror = reject
  reader.readAsDataURL(file)
})

// 委托处理消息HTML中的图片点击，打开预览
const onMessageHtmlClick = (e) => {
  const img = e.target.closest('img')
  if (!img) return
  const src = img.getAttribute('src')
  if (!src) return
  const proxied = /^\/api\/v1\/content\/proxy\?/.test(src) ? src : `/api/v1/content/proxy?url=${encodeURIComponent(src)}`
  imagePreviewList.value = [proxied]
  imagePreviewIndex.value = 0
  imageViewerVisible.value = true
}

const selectedModel = ref(null)
const temperature = ref(0.8)
const inputMessage = ref('')
const isGenerating = ref(false)
const messageContents = ref(new Map()) // 存储消息的HTML内容
const messagesContainer = ref()
const latexRenderEnabled = ref(true) // LaTeX渲染开关，默认开启

// 计费显示
const lastUsageTokens = ref(null) // {prompt: number, completion: number, total: number}
const lastCostYuan = ref('')
const lastCostBreakdown = ref('')

// 计算属性
const selectedModelName = computed(() => {
  const model = models.value.find(m => m.id === selectedModel.value)
  return model?.name || selectedModel.value
})

// 生命周期
onMounted(async () => {
  // 从localStorage加载LaTeX渲染设置
  const savedLatexSetting = localStorage.getItem('latexRenderEnabled')
  if (savedLatexSetting !== null) {
    latexRenderEnabled.value = savedLatexSetting === 'true'
  }

  await loadInitialData()

  // 添加代码复制按钮的事件监听器
  setupCodeCopyListeners()
})

// 组件销毁时清理事件监听器
onUnmounted(() => {
  document.removeEventListener('click', handleCopyButtonClick)
})

// 设置代码复制按钮的事件监听器
const setupCodeCopyListeners = () => {
  // 使用事件委托来处理动态生成的复制按钮
  document.addEventListener('click', handleCopyButtonClick)
}

// 处理复制按钮点击事件
const handleCopyButtonClick = async (event) => {
  const target = event.target.closest('.enhanced-copy-button') || event.target.closest('.copy-button')
  if (!target) return

  const encodedCode = target.getAttribute('data-code')
  if (!encodedCode) return

  event.preventDefault()
  event.stopPropagation()

  // 添加点击效果
  target.classList.add('copying')

  try {
    await copyCodeToClipboard(encodedCode)

    // 更新按钮文本显示复制成功
    const copyText = target.querySelector('.copy-text')
    if (copyText) {
      const originalText = copyText.textContent
      copyText.textContent = '已复制!'
      setTimeout(() => {
        copyText.textContent = originalText
      }, 2000)
    }
  } catch (error) {
    console.error('复制失败:', error)
  } finally {
    // 移除点击效果
    setTimeout(() => {
      target.classList.remove('copying')
    }, 200)
  }
}

// 方法
// LaTeX渲染开关处理
const onLatexRenderToggle = () => {
  // 保存设置到localStorage
  localStorage.setItem('latexRenderEnabled', latexRenderEnabled.value.toString())

  // 重新渲染所有消息
  messages.value.forEach(message => {
    loadMessageContent(message)
  })

  ElMessage.success(latexRenderEnabled.value ? 'LaTeX渲染已开启' : 'LaTeX渲染已关闭')
}



// 处理消息内容，为代码块添加复制功能和语法高亮
const processMessageContentWithCodeBlocks = (htmlContent) => {
  if (!htmlContent) return htmlContent

  // 使用字符串替换的方式处理代码块，避免DOM操作的复杂性
  let processedContent = htmlContent

  // 为代码块添加复制功能和语法高亮
  processedContent = processedContent.replace(/<pre><code([^>]*)>([\s\S]*?)<\/code><\/pre>/g, (_, attributes, codeContent) => {
    // 提取语言信息
    const languageMatch = attributes.match(/class="language-(\w+)"/) || attributes.match(/class="(\w+)"/)
    const language = languageMatch ? languageMatch[1] : ''

    // 提取纯文本内容（去除HTML标签）
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = codeContent
    const plainText = tempDiv.textContent || tempDiv.innerText || ''

    // 应用语法高亮
    let highlightedCode = ''
    try {
      if (language && hljs.getLanguage(language)) {
        // 如果指定了语言且支持，使用指定语言高亮
        highlightedCode = hljs.highlight(plainText, { language }).value
      } else {
        // 否则尝试自动检测语言
        const result = hljs.highlightAuto(plainText)
        highlightedCode = result.value
        // 如果自动检测到语言，更新language变量用于显示
        if (result.language && !language) {
          // language = result.language // 暂时不更新，保持原有逻辑
        }
      }
    } catch (e) {
      console.warn('代码高亮失败:', e)
      highlightedCode = plainText // 降级到纯文本
    }

    // 生成唯一ID
    const copyId = `copy-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`

    return `
      <div class="enhanced-code-block" data-language="${language}">
        <div class="code-block-header">
          ${language ? `<div class="code-language-tag">${language.toUpperCase()}</div>` : ''}
          <button class="enhanced-copy-button" data-copy-id="${copyId}" data-code="${encodeURIComponent(plainText)}">
            <svg class="copy-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
            <span class="copy-text">复制代码</span>
          </button>
        </div>
        <div class="code-content">
          <pre><code class="hljs ${language ? `language-${language}` : ''}">${highlightedCode}</code></pre>
        </div>
      </div>
    `
  })

  return processedContent
}

// 复制代码到剪贴板的改进版本
const copyCodeToClipboard = async (encodedText) => {
  try {
    const text = decodeURIComponent(encodedText)
    await navigator.clipboard.writeText(text)
    ElMessage.success('代码已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
    // 降级方案
    try {
      const textArea = document.createElement('textarea')
      textArea.value = decodeURIComponent(encodedText)
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('代码已复制到剪贴板')
    } catch (fallbackErr) {
      console.error('降级复制也失败:', fallbackErr)
      ElMessage.error('复制失败，请手动选择复制')
    }
  }
}

const loadInitialData = async () => {
  try {
    appStore.setLoading(true, '加载数据中...')

    // 加载模型列表
    await loadModels()

    // 加载对话列表
    await loadConversations()

    // 如果有对话，选择第一个
    if (conversations.value.length > 0) {
      await selectConversation(conversations.value[0].id)
    }
  } catch (error) {
    console.error('加载初始数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    appStore.setLoading(false)
  }
}

const loadModels = async () => {
  try {
    console.log('🔄 正在加载模型列表...')
    const response = await get('/api/v1/models/')
    console.log('📋 模型API响应:', response)

    if (response.success && response.models) {
      models.value = response.models.map(model => ({
        id: model.id,
        name: model.display_name || model.name || model.id,
        description: model.description,
        in_cost: model.input_cost_per_token || (model.pricing && model.pricing.input_token) || 0,
        out_cost: model.output_cost_per_token || (model.pricing && model.pricing.output_token) || 0,
      }))
      console.log('✅ 模型加载成功:', models.value)

      // 设置默认选中第一个模型
      if (models.value.length > 0) {
        selectedModel.value = models.value[0].id
        console.log('🎯 设置默认模型:', models.value[0].name)
      }
    } else {
      console.error('❌ 模型API返回格式错误:', response)
      ElMessage.error('获取模型列表失败')
    }
  } catch (error) {
    console.error('❌ 加载模型失败:', error)
    ElMessage.error('连接模型服务失败，请检查网络连接')
  }
}

const loadConversations = async () => {
  try {
    console.log('🔄 正在加载对话列表...')
    const response = await get('/api/v1/conversations/')
    console.log('💬 对话API响应:', response)

    if (response.success && response.conversations) {
      conversations.value = response.conversations
      console.log('✅ 对话加载成功:', conversations.value)
    } else {
      console.log('📝 暂无对话历史')
      conversations.value = []
    }
  } catch (error) {
    console.error('❌ 加载对话失败:', error)
    conversations.value = []
  }
}

const selectConversation = async (conversationId) => {
  try {
    console.log('🔄 选择对话:', conversationId)

    // 设置当前对话ID
    currentConversationId.value = conversationId

    // 调用后端API设置当前对话（使用 fetch，避免全局拦截器弹出误导性错误提示）
    try {
      const resp = await fetch(`/api/v1/conversations/${conversationId}/set-current`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token') || ''}`,
          'Content-Type': 'application/json'
        }
      })
      if (resp.ok) {
        const data = await resp.json().catch(() => null)
        if (data?.success) {
          console.log('✅ 当前对话设置成功:', data.message)
        }
      } else {
        console.warn('⚠️ 设置当前对话非200返回，但继续:', resp.status)
      }
    } catch (error) {
      console.warn('⚠️ 设置当前对话失败，但继续加载消息:', error)
    }

    // 加载对话消息
    await loadMessages(conversationId)
  } catch (error) {
    console.error('❌ 选择对话失败:', error)
    ElMessage.error('选择对话失败')
  }
}

const loadMessages = async (conversationId) => {
  try {
    console.log('🔄 加载对话消息:', conversationId)
    const response = await get(`/api/v1/chat/messages/${conversationId}`)
    console.log('💬 消息API响应:', response)

    if (response.success && response.messages) {
      messages.value = response.messages.map(msg => ({
        id: msg.id,
        role: msg.role,
        content: msg.content,
        created_at: new Date(msg.created_at),
        content_url: msg.content_url,
        plain_html_url: msg.plain_html_url,
        content_type: msg.content_type,
        // 以下为旧字段，兼容处理（可能为空）
        rendered_katex_url: msg.rendered_katex_url,
        rendered_plain_url: msg.rendered_plain_url,
        original_url: msg.original_url,
        is_error: msg.is_error,
        error_info: msg.error_info
      }))
      console.log('✅ 消息加载成功:', messages.value.length, '条消息')

      // 异步加载所有消息的HTML内容
      messages.value.forEach(message => {
        loadMessageContent(message)
      })
    } else {
      console.log('📝 该对话暂无消息历史')
      messages.value = []
    }

    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('❌ 加载消息失败:', error)
    messages.value = []
  }
}

const createNewConversation = async () => {
  try {
    console.log('🆕 创建新对话...')
    const response = await post('/api/v1/conversations/', {
      title: '新对话'
    })

    if (response.success && response.conversation) {
      const newConv = response.conversation
      conversations.value.unshift(newConv)
      await selectConversation(newConv.id)
      console.log('✅ 新对话创建成功:', newConv)
    } else {
      // 如果API不支持，使用临时ID
      const tempId = Date.now()
      currentConversationId.value = tempId
      console.log('📝 使用临时对话ID:', tempId)
    }
  } catch (error) {
    console.error('❌ 创建对话失败:', error)
    // 使用临时ID作为fallback
    const tempId = Date.now()
    currentConversationId.value = tempId
    console.log('📝 使用临时对话ID作为fallback:', tempId)
  }
}

const deleteConversation = async (conversationId) => {
  try {
    console.log('🗑️ 删除对话:', conversationId)

    // 确认删除
    await ElMessageBox.confirm(
      '确定要删除这个对话吗？此操作不可撤销。',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await fetch(`/api/v1/conversations/${conversationId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token') || ''}` ,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        // 从列表中移除对话
        conversations.value = conversations.value.filter(conv => conv.id !== conversationId)

        // 如果删除的是当前对话，清空当前对话
        if (currentConversationId.value === conversationId) {
          currentConversationId.value = null
          messages.value = []
        }

        ElMessage.success('对话删除成功')
        console.log('✅ 对话删除成功:', conversationId)
      } else {
        throw new Error(result.message || '删除失败')
      }
    } else {
      throw new Error(`HTTP ${response.status}`)
    }
  } catch (error) {
    if (error.message !== 'cancel') {
      console.error('❌ 删除对话失败:', error)
      ElMessage.error('删除对话失败: ' + error.message)
    }
  }
}

// 更新对话标题
const updateConversationTitle = async (conversationId, messageText) => {
  try {
    // 提取消息的前15个字符作为标题
    const title = messageText.length > 15 ? messageText.substring(0, 15) + '...' : messageText

    console.log('📝 更新对话标题:', conversationId, title)

    const response = await post(`/api/v1/conversations/${conversationId}/title`, {
      title: title
    })

    if (response.success) {
      // 更新本地对话列表中的标题
      const conversation = conversations.value.find(conv => conv.id === conversationId)
      if (conversation) {
        conversation.title = title
      }
      console.log('✅ 对话标题更新成功:', title)
    } else {
      console.warn('⚠️ 对话标题更新失败:', response.message)
    }
  } catch (error) {
    console.error('❌ 更新对话标题失败:', error)
    // 不显示错误消息给用户，因为这不是关键功能
  }
}

// 删除消息
const deleteMessage = async (messageId) => {
  try {
    console.log('🗑️ 删除消息:', messageId)

    // 确认删除
    await ElMessageBox.confirm(
      '确定要删除这条消息吗？此操作不可撤销。',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await fetch(`/api/v1/chat/messages/${messageId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token') || ''}` ,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        // 从本地消息列表中移除消息
        messages.value = messages.value.filter(msg => msg.id !== messageId)

        // 清理消息内容缓存
        messageContents.value.delete(messageId)

        ElMessage.success('消息删除成功')
        console.log('✅ 消息删除成功:', messageId)
      } else {
        throw new Error(result.message || '删除失败')
      }
    } else {
      throw new Error(`HTTP ${response.status}`)
    }
  } catch (error) {
    if (error.message !== 'cancel') {
      console.error('❌ 删除消息失败:', error)
      ElMessage.error('删除消息失败: ' + error.message)
    }
  }
}

const sendMessage = async () => {
  if (!inputMessage.value.trim() || isGenerating.value) return

  try {
    isGenerating.value = true

    // 确保有对话ID
    if (!currentConversationId.value) {
      await createNewConversation()
    }

    // 检查是否是新对话的第一条消息
    const isFirstMessage = messages.value.length === 0
    console.log('🔍 检查是否为第一条消息:', {
      isFirstMessage,
      messagesCount: messages.value.length,
      conversationId: currentConversationId.value
    })

    // 创建临时用户消息（显示原始内容，稍后会被替换）
    const tempUserMessage = {
      id: Date.now(),
      role: 'user',
      content: inputMessage.value,
      created_at: new Date(),
      content_url: null,
      is_loading: true
    }

    messages.value.push(tempUserMessage)
    const messageText = inputMessage.value
    inputMessage.value = ''

    await nextTick()
    scrollToBottom()

    console.log('🚀 发送聊天请求:', {
      conversation_id: currentConversationId.value,
      message: messageText,
      model_name: selectedModel.value,
      temperature: temperature.value
    })

    // 调用真实的聊天API（附带临时图片ID）
    const response = await post('/api/v1/chat/', {
      conversation_id: currentConversationId.value,
      message: messageText,
      model_name: selectedModel.value,
      temperature: temperature.value,
      max_tokens: 2000,
      stream: false,
      attachments: pendingAttachments.value.length ? pendingAttachments.value : undefined
    })
    // 清空待发送的附件列表与预览（后端已清理临时图片）
    pendingAttachments.value = []
    pendingPreviews.value = []

    console.log('🤖 聊天API响应:', response)

    // 显示本次usage/cost
    if (response.usage) {
      const prompt = response.usage.prompt_tokens || response.usage.input_tokens || 0
      const completion = response.usage.completion_tokens || response.usage.output_tokens || 0
      lastUsageTokens.value = { prompt, completion, total: prompt + completion }
    } else {
      lastUsageTokens.value = null
    }
    if (typeof response.cost === 'number') {
      // 保留4位小数
      lastCostYuan.value = Number(response.cost).toFixed(4)

      // 展示单价与计算细节 + 控制台打印
      const model = models.value.find(m => m.id === selectedModel.value)
      const inCost = model?.in_cost ?? null
      const outCost = model?.out_cost ?? null
      if (lastUsageTokens.value && (inCost !== null) && (outCost !== null)) {
        lastCostBreakdown.value = `单价: 入 ¥${inCost}/tok, 出 ¥${outCost}/tok；计算: ${lastUsageTokens.value.prompt}*${inCost} + ${lastUsageTokens.value.completion}*${outCost}`
        console.log('[计费调试]', {
          model: selectedModel.value,
          in_cost_per_token: inCost,
          out_cost_per_token: outCost,
          prompt_tokens: lastUsageTokens.value.prompt,
          completion_tokens: lastUsageTokens.value.completion,
          expected_cost: (lastUsageTokens.value.prompt * inCost + lastUsageTokens.value.completion * outCost)
        })
      } else {
        lastCostBreakdown.value = ''
        console.warn('[计费调试] 无法获取模型单价或用量', { model: selectedModel.value, modelObj: model, usage: lastUsageTokens.value })
      }
    } else {
      lastCostYuan.value = ''
      lastCostBreakdown.value = ''
    }

    // 刷新余额显示（从后端获取最新用户信息）
    try {
      const updatedUser = await post('/api/v1/users/refresh-info')
      if (updatedUser && (updatedUser.current_balance !== undefined)) {
        authStore.updateUserInfo(updatedUser)
      }
    } catch (e) {
      console.warn('刷新用户余额失败:', e)
    }

    if (response.success && response.response) {
      // 更新用户消息为完整信息（如果有的话）
      if (response.user_message) {
        const userMsgIndex = messages.value.findIndex(msg => msg.id === tempUserMessage.id)
        if (userMsgIndex !== -1) {
          const updatedUserMessage = {
            id: response.user_message.id,
            role: response.user_message.role,
            content: messageText, // 使用原始输入的文本，而不是从API返回的content
            created_at: new Date(response.user_message.created_at),
            content_url: response.user_message.content_url,
            plain_html_url: response.user_message.plain_html_url,
            content_type: response.user_message.content_type,
            is_error: response.user_message.is_error,
            error_info: response.user_message.error_info
          }
          messages.value[userMsgIndex] = updatedUserMessage
          console.log('✅ 用户消息已更新，显示URL:', getMessageDisplayUrl(updatedUserMessage))

          // 加载用户消息的HTML内容
          loadMessageContent(updatedUserMessage)
        }
      }

      // 添加AI消息
      const aiMessage = {
        id: response.assistant_message_id || Date.now() + 1,
        role: 'assistant',
        content: response.assistant_message?.content || response.response,
        created_at: new Date(response.assistant_message?.created_at || new Date()),
        content_url: response.assistant_message?.content_url,
        plain_html_url: response.assistant_message?.plain_html_url,
        content_type: response.assistant_message?.content_type,
        is_error: response.assistant_message?.is_error,
        error_info: response.assistant_message?.error_info
      }

      messages.value.push(aiMessage)
      console.log('✅ AI消息已添加，显示URL:', getMessageDisplayUrl(aiMessage))

      // 加载AI消息的HTML内容
      loadMessageContent(aiMessage)

      // 如果是第一条消息，自动更新对话标题
      if (isFirstMessage && currentConversationId.value) {
        await updateConversationTitle(currentConversationId.value, messageText)
      }

      await nextTick()
      scrollToBottom()
    } else {
      throw new Error(response.error || '获取AI回复失败')
    }

  } catch (error) {
    console.error('❌ 发送消息失败:', error)
    ElMessage.error(error.message || '发送消息失败')
  } finally {
    isGenerating.value = false
  }
}

const toggleSidebar = () => {
  appStore.toggleSidebar()
}

const logout = async () => {
  try {
    await authStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('登出失败:', error)
  }
}

const formatTime = (time) => {
  return new Date(time).toLocaleTimeString()
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 选择合适的消息URL用于显示（前端数学渲染模式）
const getMessageDisplayUrl = (message) => {
  // 新接口：优先使用 plain_html_url，其次回退到 content_url
  if (message.plain_html_url) return message.plain_html_url
  return message.content_url
}

// 从MinIO URL获取HTML内容并提取消息部分
const fetchMessageContent = async (url) => {
  if (!url || url.startsWith('mock://') || url.startsWith('placeholder://')) {


    return null
  }

  try {
    // 通过后端代理获取内容，避免跨域/CORS问题
    const proxyUrl = `/api/v1/content/proxy?url=${encodeURIComponent(url)}`
    const response = await fetch(proxyUrl)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }

    const htmlContent = await response.text()

    // 如果是JSON格式（原始消息），提取content字段
    if (url.includes('.json')) {
      try {
        const jsonData = JSON.parse(htmlContent)
        return jsonData.content || htmlContent
      } catch (e) {
        return htmlContent
      }
    }

    // 如果是HTML格式，提取message-content div的内容
    const parser = new DOMParser()
    const doc = parser.parseFromString(htmlContent, 'text/html')
    const messageContentDiv = doc.querySelector('.message-content')

    if (messageContentDiv) {
      return messageContentDiv.innerHTML
    }

    // 如果没有找到message-content div，返回body内容
    const body = doc.querySelector('body')
    return body ? body.innerHTML : htmlContent

  } catch (error) {
    console.error('获取消息内容失败:', error)
    return null
  }
}

// 加载消息的HTML内容
const loadMessageContent = async (message) => {
  const url = getMessageDisplayUrl(message)
  if (!url && !message.content) {
    return
  }

  let content = (url ? await fetchMessageContent(url) : null) || message.content
  if (content) {
    // 重写资源链接，统一走后端代理
    try {
      const parser = new DOMParser()
      const doc = parser.parseFromString(content, 'text/html')
      const rewrite = (el, attr) => {
        const v = el.getAttribute(attr)
        if (!v) return
        if (/^https?:\/\//i.test(v)) {
          el.setAttribute(attr, `/api/v1/content/proxy?url=${encodeURIComponent(v)}`)
        }
      }
      doc.querySelectorAll('img[src]').forEach(el => rewrite(el, 'src'))
      doc.querySelectorAll('a[href]').forEach(el => rewrite(el, 'href'))
      doc.querySelectorAll('link[href]').forEach(el => rewrite(el, 'href'))
      doc.querySelectorAll('script[src]').forEach(el => rewrite(el, 'src'))
      // 统一限制图片尺寸并去掉内联宽高以免撑开
      doc.querySelectorAll('img').forEach(el => {
        el.removeAttribute('width');
        el.removeAttribute('height');
        if (el.style) {
          el.style.maxWidth = '100%'
          el.style.maxHeight = '360px'
          el.style.width = 'auto'
          el.style.height = 'auto'
          el.style.objectFit = 'contain'
          el.style.cursor = 'zoom-in'
        }
      })
      content = doc.body ? doc.body.innerHTML : content
    } catch (e) {
      console.warn('重写资源链接失败', e)
    }
    messageContents.value.set(message.id, content)
  }
}

// KaTeX渲染函数 - 使用占位符方法，简单可靠
const renderMathInContent = (htmlContent) => {
  // 如果LaTeX渲染被禁用，直接返回原始内容
  if (!latexRenderEnabled.value) {
    return htmlContent
  }

  try {
    let content = htmlContent
    const mathPlaceholders = []
    let placeholderIndex = 0

    // 1. 先处理块级公式 $$...$$（支持多行）
    content = content.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
      const placeholder = `__MATH_BLOCK_${placeholderIndex}__`
      try {
        const rendered = katex.renderToString(formula.trim(), {
          displayMode: true,
          throwOnError: false
        })
        mathPlaceholders[placeholderIndex] = `<div class="katex-block-wrapper">${rendered}</div>`
      } catch (e) {
        console.warn('KaTeX块级公式渲染失败:', formula, e)
        mathPlaceholders[placeholderIndex] = match // 保留原始内容
      }
      placeholderIndex++
      return placeholder
    })

    // 2. 再处理行内公式 $...$（不包含换行符）
    content = content.replace(/\$([^$\n]+)\$/g, (match, formula) => {
      const placeholder = `__MATH_INLINE_${placeholderIndex}__`
      try {
        const rendered = katex.renderToString(formula.trim(), {
          displayMode: false,
          throwOnError: false
        })
        mathPlaceholders[placeholderIndex] = `<span class="katex-inline-wrapper">${rendered}</span>`
      } catch (e) {
        console.warn('KaTeX行内公式渲染失败:', formula, e)
        mathPlaceholders[placeholderIndex] = match // 保留原始内容
      }
      placeholderIndex++
      return placeholder
    })

    // 3. 将占位符替换为渲染后的内容
    mathPlaceholders.forEach((rendered, index) => {
      content = content.replace(`__MATH_BLOCK_${index}__`, rendered)
      content = content.replace(`__MATH_INLINE_${index}__`, rendered)
    })

    return content
  } catch (e) {
    console.error('KaTeX渲染过程出错:', e)
    return htmlContent // 返回原始内容
  }
}

// 获取消息的显示内容
const getMessageContent = (message) => {
  const rawContent = messageContents.value.get(message.id) || message.content || '加载中...'

  // 检查用户是否启用了LaTeX渲染
  const userSettings = authStore.user?.settings || {}
  const enableLatex = userSettings.mathjax !== false // 默认启用LaTeX

  if (enableLatex && rawContent && rawContent !== '加载中...') {
    // 对内容进行KaTeX渲染
    return renderMathInContent(rawContent)
  }

  return rawContent
}

// 移除了iframe相关代码，现在使用v-html直接渲染


</script>

<style scoped>
.chat-container {
  display: flex;
  height: 100vh;
  background: var(--bg-color-page);
}

.sidebar {
  width: 300px;
  background: var(--bg-color);
  border-right: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  transition: var(--transition-base);
}

.sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-header h2 {
  font-size: var(--font-size-large);
  color: var(--text-primary);
  margin: 0;
}

.conversations-section {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
  font-weight: 600;
  color: var(--text-primary);
}

.conversation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-base);
  transition: var(--transition-base);
  margin-bottom: var(--spacing-xs);
}

.conversation-content {
  display: flex;
  align-items: center;
  flex: 1;
  cursor: pointer;
}

.delete-btn {
  opacity: 0;
  transition: opacity 0.2s;
}

.conversation-item:hover {
  background: var(--border-extra-light);
}

.conversation-item:hover .delete-btn {
  opacity: 1;
}

.conversation-item.active {
  background: var(--primary-color);
  color: white;
}

.conversation-title {
  margin-left: var(--spacing-sm);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.model-section, .user-section {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-light);
}

.temperature-setting {
  margin-top: var(--spacing-sm);
}

.temperature-setting label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-small);
  color: var(--text-regular);
}

.user-info {
  margin-bottom: var(--spacing-sm);
}

.user-name {
  font-weight: 600;
  color: var(--text-primary);
}

.user-balance {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
}

.message-wrapper {
  margin-bottom: var(--spacing-md);
}

.message-wrapper.user {
  display: flex;
  justify-content: flex-end;
}

.message-wrapper.assistant {
  display: flex;
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  background: var(--bg-color);
  border-radius: var(--border-radius-base);
  padding: var(--spacing-md);
  box-shadow: var(--box-shadow-base);
  position: relative;
}

.message-wrapper.user .message-content {
  background: var(--primary-color);
  color: white;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-small);
}

.message-role {
  font-weight: 600;
}

.message-time {
  opacity: 0.7;
}

.message-html-content {
  width: 100%;
  line-height: 1.75;
  color: #1f2937;
  background: #ffffff;
  border-radius: 12px;
  padding: 18px 20px;
  border: 1px solid #e5e7eb;
  overflow-wrap: break-word;
  word-wrap: break-word;
  font-size: 16px;
}

.message-html-content p {
  margin: 0 0 12px 0;
}

.message-html-content p:last-child {
  margin-bottom: 0;
}

.message-html-content pre {
  background: #f1f3f4;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 8px 0;
}

.message-html-content code {
  background: #f1f3f4;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.9em;
}

.message-html-content blockquote {
  border-left: 4px solid #ddd;
  margin: 8px 0;
  padding-left: 16px;
  color: #666;
}

.message-html-content a {
  color: #007bff;
  text-decoration: none;
}

.message-html-content a:hover {
  text-decoration: underline;
}

.message-html-content strong {
  font-weight: 600;
}

.message-html-content em {
  font-style: italic;
}

.message-text {
  line-height: 1.75;
  font-size: 16px;
  color: #1f2937;
}

.message-error {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: var(--danger-color-light);
  border: 1px solid var(--danger-color);
  border-radius: var(--border-radius-base);
  color: var(--danger-color);
  font-size: var(--font-size-small);
}



/* 消息操作按钮 */
.message-actions {
  position: absolute;
  bottom: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-content:hover .message-actions {
  opacity: 1;
}

.delete-message-btn {
  padding: 4px 6px !important;
  min-height: 24px !important;
  font-size: 12px !important;
}

.delete-message-btn:hover {
  background-color: var(--danger-color-light) !important;
  color: var(--danger-color) !important;
}

.generating-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
  color: var(--text-secondary);
}

.generating-indicator .el-icon {
  margin-right: var(--spacing-sm);
}

.status-bar {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-color);
  border-top: 1px solid var(--border-light);
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

.input-area {
  padding: var(--spacing-md);
  background: var(--bg-color);
  border-top: 1px solid var(--border-light);
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-sm);
}

/* KaTeX数学公式样式 */
.katex-block-wrapper {
  display: block;
  text-align: center;
  margin: 1em 0;
}

.katex-inline-wrapper {
  display: inline;
}

/* 确保KaTeX公式在消息中正确显示 */
.message-html-content .katex {
  font-size: 1em;
}

.message-html-content .katex-display {
  margin: 1em 0;
  text-align: center;
}

/* 待发送的图片预览区样式 */
.pending-attachments {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin: 8px 0 6px 0;
}
.pending-attachments .pending-thumb {
  border: 1px solid var(--border-light);
  padding: 6px;
  border-radius: 6px;
  background: #fafafa;
}
.pending-attachments .pending-thumb img {
  width: 96px;
  height: 96px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  display: block;
}
.pending-attachments .pending-thumb .name {
  display: block;
  max-width: 96px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
  margin-top: 4px;
}
.upload-progress { margin: 6px 0; }

/* 图片在消息栏内的展示样式（最大宽度、圆角、可点击） */
.message-html-content img {
  max-width: 100%;
  max-height: 360px; /* 限制图片最大高度，防止过大撑开 */
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 6px;
  cursor: zoom-in;
  display: block;
  margin: 6px 0;
}

/* LaTeX设置样式 */
.latex-setting {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--border-light);
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.setting-row label {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin: 0;
}

/* 增强代码块样式 - 类似编辑器 */
.enhanced-code-block {
  position: relative;
  margin: 20px 0;
  border-radius: 12px;
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(17, 24, 39, 0.06);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', 'Courier New', monospace;
}

.code-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #eef2f7;
  border-bottom: 1px solid #e5e7eb;
  min-height: 48px;
}

.code-language-tag {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: #e5e7eb;
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
}

.enhanced-copy-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  font-size: 13px;
  font-weight: 500;
  background: #10b981;
  border: 1px solid #34d399;
  border-radius: 8px;
  cursor: pointer;
  color: #ffffff;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.enhanced-copy-button:hover {
  background: #059669;
  border-color: #10b981;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.enhanced-copy-button:active,
.enhanced-copy-button.copying {
  background: #047857;
  transform: translateY(0);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.08);
}

.code-content {
  position: relative;
}

.code-content pre {
  margin: 0;
  padding: 20px;
  background: transparent;
  border-radius: 0;
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.7;
  color: #111827;
}

.code-content code {
  background: transparent !important;
  padding: 0 !important;
  border-radius: 0 !important;
  font-family: inherit;
  color: inherit;
}

/* 原有代码块容器样式 */
.code-block-container {
  position: relative;
  margin: 16px 0;
  border-radius: 8px;
  background: #f8f9fa;
  border: 1px solid #e1e4e8;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.code-language-label {
  position: absolute;
  top: 8px;
  left: 12px;
  font-size: 11px;
  font-weight: 600;
  color: #586069;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 6px;
  border-radius: 3px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 5;
}

.code-header,
:deep(.code-header) {
  position: absolute;
  top: 0;
  right: 0;
  padding: 8px;
  z-index: 10;
}

.copy-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #d1d5da;
  border-radius: 6px;
  cursor: pointer;
  color: #586069;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.copy-button:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: #959da5;
  color: #24292e;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.copy-button:active,
.copy-button.copying {
  background: #e1e4e8;
  transform: translateY(0);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.copy-icon {
  width: 14px;
  height: 14px;
  stroke-width: 1.5;
}

.copy-text {
  font-weight: 500;
  white-space: nowrap;
}

.code-block-container pre {
  margin: 0;
  padding: 16px;
  padding-top: 40px; /* 为语言标签和复制按钮留出空间 */
  background: transparent;
  border-radius: 0;
  overflow-x: auto;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.code-block-container code {
  background: transparent !important;
  padding: 0 !important;
  border-radius: 0 !important;
  font-family: inherit;
}

/* 数学公式容器样式 */
.math-container {
  position: relative;
  display: inline-block;
}

.math-copy {
  position: absolute;
  top: -2px;
  right: -2px;
  font-size: 10px;
  padding: 2px 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.math-container:hover .math-copy {
  opacity: 1;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .code-block-container,
  :deep(.code-block-container) {
    background: #0d1117;
    border-color: #21262d;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.35);
  }

  .code-language-label {
    color: #7d8590;
    background: rgba(13, 17, 23, 0.8);
  }

  .copy-button,
  :deep(.copy-button) {
    background: rgba(13, 17, 23, 0.9);
    border-color: #30363d;
    color: #7d8590;
  }

  .copy-button:hover,
  :deep(.copy-button:hover) {
    background: rgba(13, 17, 23, 0.95);
    border-color: #6e7681;
    color: #f0f6fc;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .copy-button:active,
  .copy-button.copying,
  :deep(.copy-button:active),
  :deep(.copy-button.copying) {
    background: #21262d;
  }

  .code-block-container pre,
  :deep(.code-block-container pre) {
    color: #e6edf3;
  }

  .code-block-container code,
  :deep(.code-block-container code) {
    color: #e6edf3;
  }
}
</style>
