<template>
  <div class="profile-container">
    <div class="profile-header">
      <h1>个人资料</h1>
    </div>
    
    <div class="profile-content">
      <el-card class="profile-card">
        <template #header>
          <span>用户信息</span>
        </template>
        
        <div class="user-info">
          <div class="info-item">
            <label>用户名:</label>
            <span>{{ userInfo?.username || '未知用户' }}</span>
          </div>
          
          <div class="info-item">
            <label>用户ID:</label>
            <span>{{ userInfo?.id || '-' }}</span>
          </div>
          
          <div class="info-item">
            <label>账户余额:</label>
            <span class="balance">¥{{ userInfo?.current_balance || 0 }}</span>
          </div>
          
          <div class="info-item">
            <label>总充值:</label>
            <span>¥{{ userInfo?.total_deposited || 0 }}</span>
          </div>
          
          <div class="info-item">
            <label>总消费:</label>
            <span>¥{{ userInfo?.total_spent || 0 }}</span>
          </div>
          
          <div class="info-item">
            <label>创建时间:</label>
            <span>{{ formatDate(userInfo?.created_at) }}</span>
          </div>
        </div>
      </el-card>
      
      <el-card class="stats-card">
        <template #header>
          <span>使用统计</span>
        </template>
        
        <div class="stats-info">
          <div class="stat-item">
            <div class="stat-value">{{ userInfo?.total_prompt_tokens || 0 }}</div>
            <div class="stat-label">输入Token数</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-value">{{ userInfo?.total_completion_tokens || 0 }}</div>
            <div class="stat-label">输出Token数</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-value">{{ totalTokens }}</div>
            <div class="stat-label">总Token数</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

const userInfo = computed(() => authStore.userInfo)

const totalTokens = computed(() => {
  const prompt = userInfo.value?.total_prompt_tokens || 0
  const completion = userInfo.value?.total_completion_tokens || 0
  return prompt + completion
})

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.profile-container {
  padding: var(--spacing-lg);
  max-width: 800px;
  margin: 0 auto;
}

.profile-header {
  margin-bottom: var(--spacing-lg);
}

.profile-header h1 {
  color: var(--text-primary);
  font-size: var(--font-size-extra-large);
}

.profile-content {
  display: grid;
  gap: var(--spacing-lg);
}

.profile-card, .stats-card {
  margin-bottom: var(--spacing-lg);
}

.user-info {
  display: grid;
  gap: var(--spacing-md);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-lighter);
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 600;
  color: var(--text-regular);
}

.balance {
  color: var(--success-color);
  font-weight: 600;
}

.stats-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-lg);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-md);
  background: var(--bg-color-page);
  border-radius: var(--border-radius-base);
}

.stat-value {
  font-size: var(--font-size-extra-large);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}
</style>
