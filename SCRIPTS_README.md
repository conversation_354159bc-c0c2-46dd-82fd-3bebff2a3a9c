# InspirFlow 部署脚本说明

本项目包含了一套完整的自动化部署脚本，用于简化 InspirFlow 应用的构建和部署过程。

## 📁 脚本文件列表

### 🚀 主要部署脚本

| 脚本名称 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `one-click-deploy.sh` | 一键部署脚本 | **推荐使用** - 完整的自动化部署流程 |
| `build-frontend.sh` | 前端构建脚本 | 单独构建前端应用 |
| `deploy.sh` | Docker Compose 部署脚本 | 部署 Docker 服务 |

### 🔧 辅助工具脚本

| 脚本名称 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `inspirflow-manager.sh` | **主管理器** | **推荐使用** - 集成所有功能的主菜单 |
| `deployment-summary.sh` | 部署状态总结 | 查看部署状态和管理信息 |
| `test-deployment.sh` | 环境测试脚本 | 验证部署环境是否就绪 |
| `stop-services.sh` | 服务停止脚本 | 安全停止所有服务 |
| `logs-viewer.sh` | 日志查看器 | 交互式查看和管理日志 |
| `backup-restore.sh` | 备份恢复工具 | 数据备份和恢复管理 |

## 🎯 快速开始

### 🌟 推荐方式：使用主管理器
```bash
# 启动集成管理界面（推荐）
./inspirflow-manager.sh
```

### 📋 传统方式：单独执行脚本

#### 1. 首次部署
```bash
# 一键完成所有部署步骤
./one-click-deploy.sh
```

#### 2. 查看部署状态
```bash
# 查看详细的部署状态和管理信息
./deployment-summary.sh
```

#### 3. 测试环境
```bash
# 验证部署环境是否正确配置
./test-deployment.sh
```

## 📋 脚本详细说明

### `one-click-deploy.sh` - 一键部署脚本
**最推荐使用的脚本**，自动执行完整的部署流程：

1. ✅ 检查前置条件（Docker、Node.js 等）
2. ✅ 构建前端应用
3. ✅ 检查并处理端口占用
4. ✅ 使用 Docker Compose v2 部署服务
5. ✅ 验证服务状态
6. ✅ 显示访问信息

**特点：**
- 🎨 友好的用户界面和彩色输出
- 🔍 详细的错误检查和处理
- 📊 实时显示部署进度
- 🎉 部署完成后显示访问地址

### `build-frontend.sh` - 前端构建脚本
专门用于构建前端应用的脚本：

1. ✅ 检查 Node.js 和 npm 环境
2. ✅ 清理旧的构建文件
3. ✅ 安装前端依赖
4. ✅ 加载环境变量
5. ✅ 执行生产构建
6. ✅ 验证构建结果

**特点：**
- 🧹 自动清理旧文件
- 📦 智能依赖管理
- 🔍 构建结果验证
- 📊 显示构建统计信息

### `deploy.sh` - Docker Compose 部署脚本
使用 Docker Compose v2 部署应用服务：

1. ✅ 检查 Docker 和 Docker Compose 环境
2. ✅ 加载环境变量配置
3. ✅ 检查并处理端口占用
4. ✅ 自动构建前端（如果需要）
5. ✅ 停止现有服务
6. ✅ 构建 Docker 镜像
7. ✅ 启动服务
8. ✅ 检查服务状态

**特点：**
- 🐳 支持 Docker Compose v2 和 v1
- 🔄 智能端口冲突处理
- 🏗️ 自动镜像构建
- 🩺 服务健康检查

### `deployment-summary.sh` - 部署状态总结脚本
提供完整的部署状态概览：

1. ✅ 检查所有服务运行状态
2. ✅ 显示访问地址（本地和外部）
3. ✅ 列出常用管理命令
4. ✅ 显示系统信息
5. ✅ 提供故障排除指南
6. ✅ 建议下一步操作

**特点：**
- 📊 全面的状态检查
- 🌐 完整的访问信息
- 🛠️ 实用的管理命令
- 🔍 详细的故障排除指南

### `test-deployment.sh` - 环境测试脚本
验证部署环境的完整性：

1. ✅ 测试脚本文件完整性
2. ✅ 验证环境变量配置
3. ✅ 检查 Docker 环境
4. ✅ 验证 Node.js 环境
5. ✅ 检查前端项目结构
6. ✅ 测试端口检查功能

**特点：**
- 🧪 全面的环境验证
- 📋 详细的测试报告
- 🔧 问题诊断建议
- ✅ 通过/失败状态显示

## 🎨 脚本特性

### 用户体验
- 🎨 **彩色输出** - 使用不同颜色区分信息类型
- 📊 **进度显示** - 实时显示操作进度
- 🔍 **详细日志** - 提供详细的操作日志
- ❓ **交互确认** - 重要操作前询问用户确认

### 错误处理
- 🛡️ **健壮性** - 完善的错误检查和处理
- 🔄 **自动恢复** - 自动处理常见问题
- 📝 **错误提示** - 清晰的错误信息和解决建议
- 🚫 **安全退出** - 遇到错误时安全退出

### 兼容性
- 🐧 **跨平台** - 支持 Linux 和 macOS
- 🐳 **Docker 版本** - 支持 Docker Compose v1 和 v2
- 📦 **包管理器** - 支持 npm、yarn 等
- 🔧 **环境适配** - 自动适配不同的运行环境

## 📞 使用建议

### 首次部署
1. 运行 `./test-deployment.sh` 检查环境
2. 运行 `./one-click-deploy.sh` 进行部署
3. 运行 `./deployment-summary.sh` 查看状态

### 日常维护
- 使用 `./deployment-summary.sh` 查看服务状态
- 使用 `docker compose logs -f` 查看实时日志
- 使用 `docker compose restart` 重启服务

### 问题排查
1. 查看 `./deployment-summary.sh` 中的故障排除部分
2. 检查 Docker 容器日志
3. 验证环境变量配置
4. 确认端口没有被占用

## 📚 相关文档

- [完整部署指南](DEPLOYMENT_GUIDE.md) - 详细的部署说明
- [Docker Compose 配置](docker-compose.yml) - 服务配置文件
- [环境变量配置](.env) - 环境变量说明

---

**提示：** 所有脚本都包含详细的帮助信息和错误处理，如果遇到问题，请查看脚本输出的错误信息和建议。
