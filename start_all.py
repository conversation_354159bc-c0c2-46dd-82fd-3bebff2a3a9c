#!/usr/bin/env python3
"""
InspirFlow 完整应用启动脚本
同时启动后端和前端服务
"""

import os
import sys
import time
import signal
import subprocess
import threading
from pathlib import Path

class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.processes = []
        self.project_root = Path(__file__).parent
        
    def start_backend(self):
        """启动后端服务"""
        print("🔧 启动后端服务...")
        
        backend_script = self.project_root / "start_backend.py"
        process = subprocess.Popen([
            sys.executable, str(backend_script)
        ], cwd=self.project_root)
        
        self.processes.append(("后端", process))
        return process
    
    def start_frontend(self):
        """启动前端服务"""
        print("🎨 启动前端服务...")
        
        frontend_script = self.project_root / "start_frontend.py"
        process = subprocess.Popen([
            sys.executable, str(frontend_script)
        ], cwd=self.project_root)
        
        self.processes.append(("前端", process))
        return process
    
    def wait_for_backend(self, timeout=30):
        """等待后端服务启动"""
        import requests
        
        print("⏳ 等待后端服务启动...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get("http://localhost:20010/docs", timeout=2)
                if response.status_code == 200:
                    print("✅ 后端服务已启动")
                    return True
            except:
                pass
            time.sleep(1)
        
        print("⚠️  后端服务启动超时")
        return False
    
    def stop_all(self):
        """停止所有服务"""
        print("\n🛑 正在停止所有服务...")
        
        for name, process in self.processes:
            if process.poll() is None:  # 进程仍在运行
                print(f"   停止{name}服务...")
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    print(f"   强制终止{name}服务...")
                    process.kill()
                    process.wait()
        
        print("✅ 所有服务已停止")
    
    def run(self):
        """运行所有服务"""
        try:
            print("🚀 InspirFlow 完整应用启动")
            print("=" * 50)
            
            # 启动后端
            backend_process = self.start_backend()
            
            # 等待后端启动
            if not self.wait_for_backend():
                print("❌ 后端服务启动失败，退出")
                self.stop_all()
                return
            
            # 启动前端
            frontend_process = self.start_frontend()
            
            print("\n" + "=" * 50)
            print("🎉 所有服务已启动!")
            print("🌐 前端地址: http://localhost:20020")
            print("🔧 后端地址: http://localhost:20010")
            print("📚 API文档: http://localhost:20010/docs")
            print("💡 按 Ctrl+C 停止所有服务")
            print("=" * 50)
            
            # 等待用户中断
            while True:
                # 检查进程状态
                for name, process in self.processes:
                    if process.poll() is not None:
                        print(f"⚠️  {name}服务意外停止")
                        self.stop_all()
                        return
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n👋 收到停止信号")
        except Exception as e:
            print(f"❌ 启动过程中出现错误: {e}")
        finally:
            self.stop_all()

def main():
    """主函数"""
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 检查必要的依赖
    try:
        import requests
    except ImportError:
        print("❌ 缺少requests库，请运行: pip install requests")
        sys.exit(1)
    
    # 创建服务管理器并运行
    manager = ServiceManager()
    
    # 注册信号处理器
    def signal_handler(signum, frame):
        manager.stop_all()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    manager.run()

if __name__ == "__main__":
    main()
