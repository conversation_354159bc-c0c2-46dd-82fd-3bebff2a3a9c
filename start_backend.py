#!/usr/bin/env python3
"""
InspirFlow FastAPI 后端启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """启动FastAPI应用"""

    # 确保在项目根目录
    os.chdir(project_root)

    # 从环境变量获取配置
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 20010))
    debug = os.getenv("DEBUG", "true").lower() == "true"

    print(f"🚀 启动 InspirFlow FastAPI 后端服务")
    print(f"📍 地址: http://{host}:{port}")
    print(f"🔧 调试模式: {'开启' if debug else '关闭'}")
    print(f"📚 API文档: http://{host}:{port}/docs")
    print(f"📖 ReDoc文档: http://{host}:{port}/redoc")
    print("-" * 50)

    # 使用虚拟环境中的Python启动uvicorn
    venv_python = project_root / "venv" / "bin" / "python"
    if not venv_python.exists():
        venv_python = sys.executable  # 回退到系统Python

    cmd = [
        str(venv_python), "-m", "uvicorn", "backend.main:app",
        "--host", host,
        "--port", str(port),
        "--log-level", "info" if debug else "warning"
    ]

    if debug:
        cmd.extend(["--reload", "--reload-dir", "backend/app"])

    # 启动服务
    subprocess.run(cmd)

if __name__ == "__main__":
    main()
