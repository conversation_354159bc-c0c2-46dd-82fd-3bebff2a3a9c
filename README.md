# InspirFlow - 智能聊天应用

[![部署状态](https://img.shields.io/badge/部署状态-✅%20运行中-brightgreen)](http://localhost:20020)
[![前端](https://img.shields.io/badge/前端-Vue.js%203-4FC08D)](http://localhost:20020)
[![后端](https://img.shields.io/badge/后端-FastAPI-009688)](http://localhost:20010)
[![Docker](https://img.shields.io/badge/Docker-Compose%20v2-2496ED)](https://docs.docker.com/compose/)

> 现代化的 AI 聊天应用，支持多模型对话、消息内容多版本处理和 MinIO 对象存储

## 🚀 快速开始

### 🌟 一键启动管理器（推荐）
```bash
./inspirflow-manager.sh
```

### 📦 一键部署
```bash
./one-click-deploy.sh
```

### 📊 查看状态
```bash
./deployment-summary.sh
```

## 🌐 访问地址

- **🎨 前端应用**: http://localhost:20020
- **🔧 后端 API**: http://localhost:20010
- **📚 API 文档**: http://localhost:20010/docs
- **❤️ 健康检查**: http://localhost:20010/health

## 🚀 核心功能

### 聊天系统工作流程
1. **用户登录** → JWT认证，获取用户信息和设置
2. **选择/创建对话** → 修改Record API中的current_conversation，消息关联到对话
3. **选择AI模型** → 与不同AI模型进行对话
4. **消息处理** → 两版本内容处理：
   - 原始markdown版本
   - 普通HTML版本
5. **内容存储** → 上传到MinIO，URL存储到数据库
6. **智能显示** → 根据用户LaTeX设置选择合适版本显示

### 主要特性
- 🔐 用户认证和权限管理
- 💬 实时聊天功能
- 🤖 多AI模型选择 (Gemini Flash 2.5, Qwen3等)
- 📝 消息历史和对话管理
- 🎨 现代化响应式UI (Element Plus)
- 📊 数学公式渲染支持 (KaTeX)
- 💾 MinIO对象存储集成
- 🔄 消息内容多版本处理

## 🏗️ 技术架构

### 后端技术栈
- **FastAPI** - 现代Python Web框架
- **Pydantic** - 数据验证和设置管理
- **httpx** - 异步HTTP客户端
- **python-jose** - JWT令牌处理
- **uvicorn** - ASGI服务器
- **MinIO** - 对象存储服务

### 前端技术栈
- **Vue.js 3** - 渐进式JavaScript框架
- **Element Plus** - Vue 3组件库
- **Vite** - 快速构建工具
- **Axios** - HTTP客户端

### 外部服务
- **Record API** - 用户和对话数据管理 (端口: 20002)
- **Model API** - AI模型服务 (端口: 20001)
- **MinIO** - 对象存储服务 (端口: 7020)

## 🔑 重要配置信息

### API密钥配置 (请妥善保管)

#### Model API 密钥
```
API密钥: sk-OgYtY6BiFrjS2Mhcf_fObEF5u6dS2pKnysODXB7F2qQ
用途: 调用AI模型服务
端点: http://**************:20001
```

#### Record API 密钥
```
管理员密钥: admin-api-key-change-in-production
测试用户密钥: umC1e0wZBvysz3ZY1eMPk6LbyQxgjCVSThtHzHqe 
端点: http://localhost:20002
```

#### MinIO 配置
```
端点: **************:7020
访问密钥: admin
秘密密钥: your-secret-key-change-in-production
存储桶: inspirflow-content
```

### 可用AI模型
- **Gemini Flash 2.5** (推荐)
- **Qwen3 235B A22B Thinking 2507**

## 📁 项目结构

```
inspirflow-fastapi/
├── backend/                    # FastAPI后端
│   ├── app/
│   │   ├── api/               # API路由
│   │   │   ├── auth.py        # 认证接口
│   │   │   ├── chat.py        # 聊天接口
│   │   │   └── conversations.py # 对话管理
│   │   ├── core/              # 核心配置
│   │   │   └── config.py      # 应用配置
│   │   ├── models/            # 数据模型
│   │   │   └── schemas.py     # Pydantic模型
│   │   └── services/          # 业务逻辑
│   │       ├── auth_service.py    # 认证服务
│   │       ├── chat_service.py    # 聊天服务
│   │       └── minio_service.py   # MinIO服务
│   ├── main.py                # FastAPI应用入口
│   └── requirements.txt       # Python依赖
├── frontend/                  # Vue.js前端
│   ├── src/
│   │   ├── components/        # Vue组件
│   │   │   ├── Chat.vue       # 聊天组件
│   │   │   └── Login.vue      # 登录组件
│   │   ├── views/             # 页面视图
│   │   └── main.js            # Vue应用入口
│   ├── package.json           # Node.js依赖
│   └── vite.config.js         # Vite配置
├── test_chat_system.py        # 系统测试脚本
└── README.md                  # 项目说明
```

## 🚀 快速启动

### 方法一：自动启动脚本

```bash
# 启动后端服务
python3 start_backend.py

# 启动前端服务 (新终端)
python3 start_frontend.py
```

### 方法二：手动启动

#### 1. 启动后端服务
```bash
cd backend
source venv/bin/activate
uvicorn main:app --host 0.0.0.0 --port 20010 --reload
```

#### 2. 启动前端服务
```bash
cd frontend
npm run dev
```

### 3. 访问应用
- **前端界面**: http://localhost:20020
- **API文档**: http://localhost:20010/docs
- **健康检查**: http://localhost:20010/health

## 🧪 系统测试

运行完整的系统测试：
```bash
python3 test_chat_system.py
```

测试覆盖：
- ✅ 用户登录认证
- ✅ 对话列表获取
- ✅ 当前对话设置
- ✅ 消息发送和接收
- ✅ AI模型回复生成
- ✅ MinIO内容存储
- ✅ 多版本内容处理

## 📋 使用说明

### 登录
使用以下任一API密钥登录：
- 测试用户: `sk-9e5bdc60f0e949e6aebbc6563c65088fl85hMHyq2xbhcfke`
- 管理员: `admin-api-key-change-in-production`

### 聊天功能
1. 登录后自动获取对话列表
2. 选择现有对话或创建新对话
3. 选择AI模型 (推荐: Gemini Flash 2.5)
4. 开始聊天，支持数学公式渲染

### 数学公式支持
系统支持LaTeX数学公式，例如：
- 行内公式: `$E = mc^2$`
- 块级公式: `$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$`

## 🔧 开发说明

### 环境要求
- Python 3.8+
- Node.js 16+
- 虚拟环境已配置

### 依赖安装
```bash
# 后端依赖 (已安装在venv中)
cd backend && source venv/bin/activate && pip install -r requirements.txt

# 前端依赖
cd frontend && npm install
```

## 🎯 功能特色

### 消息内容多版本处理
每条消息都会生成三个版本：
1. **原始版本** - 用户输入的原始markdown
2. **KaTeX版本** - 渲染数学公式的HTML
3. **普通版本** - 纯HTML格式

系统根据用户的LaTeX设置自动选择合适的版本显示。

### MinIO对象存储
- 所有消息内容存储在MinIO对象存储中
- 使用预签名URL确保安全访问
- 支持大文件和富媒体内容

### 智能对话管理
- 支持多对话并行
- 对话状态持久化
- 消息历史完整保存

---

**InspirFlow** - 让AI对话更智能、更专业！ 🚀
