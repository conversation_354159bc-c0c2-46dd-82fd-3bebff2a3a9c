#!/bin/bash

# InspirFlow 快速启动脚本

echo "🚀 InspirFlow 快速启动"
echo "======================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker 未安装，请先安装 Docker${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose 未安装，请先安装 Docker Compose${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker 环境检查通过${NC}"

# 检查 .env 文件
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚠️  .env 文件不存在，从模板创建...${NC}"
    cp .env.example .env
    chmod 600 .env
    echo -e "${YELLOW}📝 请编辑 .env 文件，配置您的API密钥${NC}"
    echo -e "${YELLOW}   特别是 MODEL_API_KEY 和其他外部服务配置${NC}"
    read -p "按回车键继续，或按 Ctrl+C 退出去编辑 .env 文件..."
fi

# 运行安全检查
echo ""
echo -e "${BLUE}🔒 运行安全检查...${NC}"
if ./security-check.sh; then
    echo -e "${GREEN}✅ 安全检查通过${NC}"
else
    echo -e "${RED}❌ 安全检查失败，请修复问题后重试${NC}"
    echo -e "${YELLOW}💡 提示：请检查 .env 文件中的密钥配置${NC}"
    exit 1
fi

# 选择部署模式
echo ""
echo "请选择部署模式："
echo "1) 生产环境 (推荐)"
echo "2) 开发环境"
read -p "请输入选择 (1-2): " choice

case $choice in
    1)
        echo -e "${BLUE}🏭 启动生产环境...${NC}"
        COMPOSE_FILE="docker-compose.yml"
        ;;
    2)
        echo -e "${BLUE}🛠️  启动开发环境...${NC}"
        COMPOSE_FILE="docker-compose.dev.yml"
        ;;
    *)
        echo -e "${RED}❌ 无效选择，使用生产环境${NC}"
        COMPOSE_FILE="docker-compose.yml"
        ;;
esac

# 停止现有服务
echo ""
echo -e "${YELLOW}🛑 停止现有服务...${NC}"
docker-compose -f $COMPOSE_FILE down 2>/dev/null || true

# 构建并启动服务
echo ""
echo -e "${BLUE}🔨 构建并启动服务...${NC}"
if docker-compose -f $COMPOSE_FILE up -d --build; then
    echo -e "${GREEN}✅ 服务启动成功${NC}"
else
    echo -e "${RED}❌ 服务启动失败${NC}"
    echo -e "${YELLOW}📋 查看错误日志：${NC}"
    docker-compose -f $COMPOSE_FILE logs
    exit 1
fi

# 等待服务启动
echo ""
echo -e "${BLUE}⏳ 等待服务启动...${NC}"
sleep 10

# 检查服务状态
echo ""
echo -e "${BLUE}📊 检查服务状态...${NC}"
docker-compose -f $COMPOSE_FILE ps

# 健康检查
echo ""
echo -e "${BLUE}🏥 健康检查...${NC}"

# 检查后端
if curl -f -s http://localhost:20010/health > /dev/null; then
    echo -e "${GREEN}✅ 后端服务正常${NC}"
else
    echo -e "${RED}❌ 后端服务异常${NC}"
fi

# 检查前端
if curl -f -s http://localhost:20020 > /dev/null; then
    echo -e "${GREEN}✅ 前端服务正常${NC}"
else
    echo -e "${RED}❌ 前端服务异常${NC}"
fi

# 显示访问信息
echo ""
echo "🎉 部署完成！"
echo "=============="
echo -e "${GREEN}📱 前端应用: http://localhost:20020${NC}"
echo -e "${GREEN}🔧 后端API: http://localhost:20010${NC}"
echo -e "${GREEN}📚 API文档: http://localhost:20010/docs${NC}"
echo -e "${GREEN}💚 健康检查: http://localhost:20010/health${NC}"

echo ""
echo "📋 常用命令："
echo "  查看日志: docker-compose -f $COMPOSE_FILE logs -f"
echo "  停止服务: docker-compose -f $COMPOSE_FILE down"
echo "  重启服务: docker-compose -f $COMPOSE_FILE restart"
echo "  查看状态: docker-compose -f $COMPOSE_FILE ps"

echo ""
echo -e "${YELLOW}💡 提示：首次使用请访问前端应用进行登录配置${NC}"
echo -e "${YELLOW}🔑 使用您在 .env 文件中配置的 API 密钥登录${NC}"
