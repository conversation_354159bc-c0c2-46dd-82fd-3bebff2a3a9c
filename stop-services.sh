#!/bin/bash

# 停止服务脚本
# 用于安全地停止所有 InspirFlow 服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示标题
show_header() {
    echo ""
    echo "=================================="
    echo "    InspirFlow 服务停止脚本"
    echo "=================================="
    echo ""
}

# 检查 Docker Compose
check_docker_compose() {
    log_info "检查 Docker Compose..."
    
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
        log_info "使用 Docker Compose v2"
    elif command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
        log_warning "使用 Docker Compose v1，建议升级到 v2"
    else
        log_error "Docker Compose 未安装"
        exit 1
    fi
}

# 显示当前服务状态
show_current_status() {
    log_info "当前服务状态："
    echo ""
    
    if $COMPOSE_CMD ps -q | grep -q .; then
        $COMPOSE_CMD ps
        echo ""
    else
        log_info "没有运行中的服务"
        return 1
    fi
}

# 确认停止操作
confirm_stop() {
    echo ""
    log_warning "准备停止所有 InspirFlow 服务"
    echo ""
    
    read -p "确认停止服务？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
}

# 停止服务
stop_services() {
    log_info "正在停止服务..."
    
    # 优雅停止服务
    $COMPOSE_CMD stop
    
    if [ $? -eq 0 ]; then
        log_success "服务已停止"
    else
        log_error "停止服务时出现错误"
        return 1
    fi
}

# 移除容器
remove_containers() {
    echo ""
    read -p "是否移除容器？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "正在移除容器..."
        
        $COMPOSE_CMD down
        
        if [ $? -eq 0 ]; then
            log_success "容器已移除"
        else
            log_error "移除容器时出现错误"
            return 1
        fi
    fi
}

# 清理资源
cleanup_resources() {
    echo ""
    read -p "是否清理未使用的 Docker 资源？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "正在清理 Docker 资源..."
        
        # 清理未使用的容器
        docker container prune -f
        
        # 清理未使用的网络
        docker network prune -f
        
        # 询问是否清理镜像
        echo ""
        read -p "是否清理未使用的镜像？(y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker image prune -f
            log_success "Docker 资源清理完成"
        else
            log_success "容器和网络清理完成"
        fi
    fi
}

# 显示端口释放信息
show_port_info() {
    log_info "检查端口释放情况..."
    
    # 从环境变量获取端口
    if [ -f ".env" ]; then
        source .env
    fi
    
    local backend_port=${BACKEND_PORT:-20010}
    local frontend_port=${FRONTEND_PORT:-20020}
    
    echo ""
    echo "端口检查结果："
    
    if lsof -Pi :$backend_port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "后端端口 $backend_port 仍被占用"
        echo "占用进程："
        lsof -Pi :$backend_port -sTCP:LISTEN
    else
        log_success "后端端口 $backend_port 已释放"
    fi
    
    if lsof -Pi :$frontend_port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "前端端口 $frontend_port 仍被占用"
        echo "占用进程："
        lsof -Pi :$frontend_port -sTCP:LISTEN
    else
        log_success "前端端口 $frontend_port 已释放"
    fi
}

# 显示重启建议
show_restart_info() {
    echo ""
    echo "=================================="
    log_info "服务管理信息"
    echo "=================================="
    echo ""
    
    echo "重新启动服务："
    echo "  完整部署: ./one-click-deploy.sh"
    echo "  仅启动服务: docker compose up -d"
    echo "  查看状态: ./deployment-summary.sh"
    echo ""
    
    echo "其他有用命令："
    echo "  查看日志: docker compose logs -f"
    echo "  重新构建: docker compose build"
    echo "  强制重建: docker compose build --no-cache"
    echo ""
}

# 主函数
main() {
    show_header
    
    # 检查是否在项目根目录
    if [ ! -f "docker-compose.yml" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    check_docker_compose
    
    # 显示当前状态
    if ! show_current_status; then
        log_info "没有需要停止的服务"
        exit 0
    fi
    
    confirm_stop
    stop_services
    remove_containers
    cleanup_resources
    show_port_info
    show_restart_info
    
    echo "=================================="
    log_success "服务停止完成"
    echo "=================================="
    echo ""
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
