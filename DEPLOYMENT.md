# InspirFlow 部署指南

## 🔒 安全配置

### 环境变量配置

1. **复制环境变量模板**
   ```bash
   cp .env.example .env
   ```

2. **修改 .env 文件中的敏感信息**
   - `JWT_SECRET_KEY`: 生成强随机密钥
   - `MODEL_API_KEY`: 您的模型API密钥
   - `RECORD_API_KEY`: 您的记录API密钥
   - `MINIO_ACCESS_KEY`: MinIO访问密钥
   - `MINIO_SECRET_KEY`: MinIO秘密密钥

3. **生成强密钥示例**
   ```bash
   # 生成 JWT 密钥
   openssl rand -hex 32
   
   # 或使用 Python
   python -c "import secrets; print(secrets.token_hex(32))"
   ```

### 重要安全提醒

⚠️ **请务必修改以下默认密钥**：
- JWT_SECRET_KEY
- MODEL_API_KEY  
- RECORD_API_KEY
- MINIO_SECRET_KEY

🔐 **安全最佳实践**：
- 不要将 `.env` 文件提交到版本控制
- 定期轮换API密钥
- 使用强随机密钥
- 在生产环境中禁用调试模式

## 🐳 Docker 部署

### 生产环境部署

1. **确保环境变量配置正确**
   ```bash
   # 检查 .env 文件
   cat .env
   ```

2. **构建并启动服务**
   ```bash
   # 构建镜像
   docker-compose build
   
   # 启动服务
   docker-compose up -d
   ```

3. **查看服务状态**
   ```bash
   # 查看运行状态
   docker-compose ps
   
   # 查看日志
   docker-compose logs -f
   ```

4. **访问应用**
   - 前端: http://localhost:20020
   - 后端API: http://localhost:20010
   - 健康检查: http://localhost:20010/health

### 开发环境部署

```bash
# 使用开发配置启动
docker-compose -f docker-compose.dev.yml up -d

# 查看开发环境日志
docker-compose -f docker-compose.dev.yml logs -f
```

### 常用 Docker 命令

```bash
# 停止服务
docker-compose down

# 重新构建并启动
docker-compose up -d --build

# 查看资源使用情况
docker-compose top

# 进入容器
docker-compose exec backend bash
docker-compose exec frontend sh

# 清理未使用的镜像
docker system prune -f
```

## 🔧 传统部署

### 后端部署

1. **安装 Python 依赖**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **配置环境变量**
   ```bash
   # 复制并修改环境变量
   cp .env.example .env
   # 编辑 .env 文件
   ```

3. **启动后端服务**
   ```bash
   python main.py
   ```

### 前端部署

1. **安装 Node.js 依赖**
   ```bash
   cd frontend
   npm install
   ```

2. **构建生产版本**
   ```bash
   npm run build
   ```

3. **使用 Nginx 部署**
   ```bash
   # 将 dist 目录内容复制到 Nginx 根目录
   cp -r dist/* /var/www/html/
   
   # 使用提供的 nginx.conf 配置
   cp nginx.conf /etc/nginx/nginx.conf
   
   # 重启 Nginx
   systemctl restart nginx
   ```

## 🔍 健康检查

### 服务健康检查

```bash
# 检查后端健康状态
curl http://localhost:20010/health

# 检查前端健康状态
curl http://localhost:20020/health

# 使用 Docker 健康检查
docker-compose ps
```

### 日志监控

```bash
# 查看应用日志
docker-compose logs -f backend
docker-compose logs -f frontend

# 查看 Nginx 日志
docker-compose exec frontend tail -f /var/log/nginx/access.log
docker-compose exec frontend tail -f /var/log/nginx/error.log
```

## 🚀 性能优化

### 生产环境优化

1. **启用 Gzip 压缩** (已在 nginx.conf 中配置)
2. **设置静态资源缓存** (已在 nginx.conf 中配置)
3. **配置 CDN** (可选)
4. **数据库连接池优化**
5. **Redis 缓存** (可选)

### 监控建议

1. **应用监控**
   - 使用 Prometheus + Grafana
   - 配置告警规则

2. **日志聚合**
   - 使用 ELK Stack
   - 配置日志轮转

3. **性能监控**
   - APM 工具集成
   - 数据库性能监控

## 🔧 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细错误信息
   docker-compose logs backend
   docker-compose logs frontend
   ```

2. **环境变量未生效**
   ```bash
   # 检查环境变量
   docker-compose exec backend env | grep -E "(JWT|API|MINIO)"
   ```

3. **网络连接问题**
   ```bash
   # 检查容器网络
   docker network ls
   docker network inspect inspirflow_inspirflow-network
   ```

4. **权限问题**
   ```bash
   # 检查文件权限
   ls -la .env
   chmod 600 .env
   ```

### 备份与恢复

```bash
# 备份配置文件
tar -czf backup-$(date +%Y%m%d).tar.gz .env docker-compose.yml

# 备份日志
docker-compose exec backend tar -czf /app/logs/backup-logs-$(date +%Y%m%d).tar.gz /app/logs/
```

## 📞 支持

如果遇到部署问题，请：

1. 检查日志文件
2. 验证环境变量配置
3. 确认网络连接
4. 查看 GitHub Issues
5. 联系技术支持
