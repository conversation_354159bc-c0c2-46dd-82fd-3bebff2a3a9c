#!/usr/bin/env python3
"""
InspirFlow 登录功能测试脚本
"""

import requests
import json
import time

# 配置
BACKEND_URL = "http://localhost:20010"
FRONTEND_URL = "http://localhost:20020"
API_KEY = "admin-api-key-change-in-production"  # 使用管理员API密钥

def test_backend_health():
    """测试后端健康检查"""
    print("🔍 测试后端健康检查...")
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 后端健康检查成功: {data}")
            return True
        else:
            print(f"❌ 后端健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
        return False

def test_frontend_access():
    """测试前端页面访问"""
    print("🔍 测试前端页面访问...")
    try:
        response = requests.get(FRONTEND_URL, timeout=10)
        if response.status_code == 200:
            print(f"✅ 前端页面访问成功: {len(response.text)} 字符")
            return True
        else:
            print(f"❌ 前端页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端连接失败: {e}")
        return False

def test_login_api():
    """测试登录API"""
    print("🔍 测试登录API...")
    try:
        login_data = {
            "api_key": API_KEY
        }
        
        response = requests.post(
            f"{BACKEND_URL}/api/v1/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 登录成功!")
            print(f"   用户ID: {data.get('user', {}).get('id', 'N/A')}")
            print(f"   访问令牌: {data.get('access_token', 'N/A')[:20]}...")
            return data.get('access_token')
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 登录API调用失败: {e}")
        return None

def test_authenticated_request(token):
    """测试认证请求"""
    print("🔍 测试认证请求...")
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(
            f"{BACKEND_URL}/api/v1/auth/me",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 认证请求成功!")
            print(f"   用户信息: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 认证请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 认证请求失败: {e}")
        return False

def test_api_proxy():
    """测试前端API代理"""
    print("🔍 测试前端API代理...")
    try:
        # 通过前端代理访问后端API
        response = requests.get(f"{FRONTEND_URL}/api/v1/auth/me", timeout=10)
        
        if response.status_code == 403:
            data = response.json()
            if data.get('detail') == 'Not authenticated':
                print("✅ 前端API代理工作正常 (返回未认证状态)")
                return True
        
        print(f"❌ 前端API代理测试异常: {response.status_code}")
        print(f"   响应: {response.text}")
        return False
    except Exception as e:
        print(f"❌ 前端API代理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 InspirFlow 系统测试开始")
    print("=" * 50)
    
    # 测试步骤
    tests = [
        ("后端健康检查", test_backend_health),
        ("前端页面访问", test_frontend_access),
        ("前端API代理", test_api_proxy),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        result = test_func()
        results.append((test_name, result))
        time.sleep(1)  # 等待1秒
    
    # 登录测试
    print(f"\n📋 登录API测试")
    token = test_login_api()
    results.append(("登录API", token is not None))
    
    if token:
        print(f"\n📋 认证请求测试")
        auth_result = test_authenticated_request(token)
        results.append(("认证请求", auth_result))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
        print("💡 您现在可以访问 http://localhost:20020 使用应用")
        print(f"🔑 登录时请使用API密钥: {API_KEY}")
    else:
        print("⚠️  部分测试失败，请检查系统配置。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
